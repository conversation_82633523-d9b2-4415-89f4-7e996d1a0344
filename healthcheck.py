#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查脚本 - 用于Docker容器健康检查
检查机器人的关键功能是否正常运行
"""

import os
import sys
import json
import time
import requests
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self.bot_token = os.getenv('BOT_TOKEN')
        self.data_dir = '/app/data'
        self.checks = []
        
    def add_check(self, name: str, func, critical: bool = True):
        """添加检查项"""
        self.checks.append({
            'name': name,
            'func': func,
            'critical': critical
        })
    
    def check_telegram_api(self) -> bool:
        """检查Telegram API连接"""
        try:
            if not self.bot_token:
                logger.error("BOT_TOKEN未设置")
                return False
            
            url = f"https://api.telegram.org/bot{self.bot_token}/getMe"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('ok'):
                    logger.info(f"✅ Telegram API连接正常: {data['result']['username']}")
                    return True
            
            logger.error(f"❌ Telegram API连接失败: {response.status_code}")
            return False
            
        except Exception as e:
            logger.error(f"❌ Telegram API检查异常: {e}")
            return False
    
    def check_data_files(self) -> bool:
        """检查数据文件完整性"""
        try:
            required_files = [
                'user_data.json',
                'transaction_log.json',
                'points_history.json',
                'orders.json'
            ]
            
            for filename in required_files:
                filepath = os.path.join(self.data_dir, filename)
                
                if not os.path.exists(filepath):
                    logger.error(f"❌ 数据文件不存在: {filename}")
                    return False
                
                # 检查文件是否为有效JSON
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        json.load(f)
                except json.JSONDecodeError:
                    logger.error(f"❌ 数据文件JSON格式错误: {filename}")
                    return False
            
            logger.info("✅ 数据文件检查通过")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据文件检查异常: {e}")
            return False
    
    def check_disk_space(self) -> bool:
        """检查磁盘空间"""
        try:
            import shutil
            
            # 检查数据目录磁盘空间
            total, used, free = shutil.disk_usage(self.data_dir)
            
            # 转换为MB
            free_mb = free // (1024 * 1024)
            total_mb = total // (1024 * 1024)
            used_percent = (used / total) * 100
            
            # 如果可用空间小于100MB或使用率超过95%，认为不健康
            if free_mb < 100 or used_percent > 95:
                logger.error(f"❌ 磁盘空间不足: 可用{free_mb}MB, 使用率{used_percent:.1f}%")
                return False
            
            logger.info(f"✅ 磁盘空间正常: 可用{free_mb}MB, 使用率{used_percent:.1f}%")
            return True
            
        except Exception as e:
            logger.error(f"❌ 磁盘空间检查异常: {e}")
            return False
    
    def check_memory_usage(self) -> bool:
        """检查内存使用"""
        try:
            import psutil
            
            # 获取当前进程内存使用
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss // (1024 * 1024)
            
            # 获取系统内存信息
            system_memory = psutil.virtual_memory()
            available_mb = system_memory.available // (1024 * 1024)
            
            # 如果进程使用超过1GB或系统可用内存少于100MB，认为不健康
            if memory_mb > 1024 or available_mb < 100:
                logger.error(f"❌ 内存使用异常: 进程{memory_mb}MB, 系统可用{available_mb}MB")
                return False
            
            logger.info(f"✅ 内存使用正常: 进程{memory_mb}MB, 系统可用{available_mb}MB")
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ 内存检查异常: {e}")
            return True  # 非关键检查，异常时返回True
    
    def check_network_connectivity(self) -> bool:
        """检查网络连接"""
        try:
            # 检查关键API端点
            endpoints = [
                'https://api.telegram.org',
                'https://fapi.binance.com/fapi/v1/ping',
                'https://fapi.coinglass.com'
            ]
            
            for endpoint in endpoints:
                try:
                    response = requests.get(endpoint, timeout=5)
                    if response.status_code not in [200, 404]:  # 404也可能是正常的
                        logger.error(f"❌ 网络连接异常: {endpoint} -> {response.status_code}")
                        return False
                except requests.RequestException as e:
                    logger.error(f"❌ 网络连接失败: {endpoint} -> {e}")
                    return False
            
            logger.info("✅ 网络连接正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ 网络连接检查异常: {e}")
            return False
    
    def check_cache_freshness(self) -> bool:
        """检查缓存数据新鲜度"""
        try:
            cache_dir = os.path.join(self.data_dir, 'cache')
            if not os.path.exists(cache_dir):
                logger.warning("⚠️ 缓存目录不存在")
                return True  # 非关键检查
            
            # 检查主缓存文件
            cache_files = [
                'cache_data_primary.json',
                'cache_data_secondary.json'
            ]
            
            current_time = time.time()
            
            for cache_file in cache_files:
                cache_path = os.path.join(cache_dir, cache_file)
                if os.path.exists(cache_path):
                    # 检查文件修改时间
                    mtime = os.path.getmtime(cache_path)
                    age_minutes = (current_time - mtime) / 60
                    
                    # 如果缓存文件超过30分钟未更新，可能有问题
                    if age_minutes > 30:
                        logger.warning(f"⚠️ 缓存文件较旧: {cache_file} ({age_minutes:.1f}分钟)")
                    else:
                        logger.info(f"✅ 缓存文件新鲜: {cache_file} ({age_minutes:.1f}分钟)")
            
            return True
            
        except Exception as e:
            logger.warning(f"⚠️ 缓存检查异常: {e}")
            return True  # 非关键检查
    
    def run_all_checks(self) -> bool:
        """运行所有健康检查"""
        logger.info("🔍 开始健康检查...")
        
        # 添加所有检查项
        self.add_check("Telegram API", self.check_telegram_api, critical=True)
        self.add_check("数据文件", self.check_data_files, critical=True)
        self.add_check("磁盘空间", self.check_disk_space, critical=True)
        self.add_check("网络连接", self.check_network_connectivity, critical=True)
        self.add_check("内存使用", self.check_memory_usage, critical=False)
        self.add_check("缓存新鲜度", self.check_cache_freshness, critical=False)
        
        # 执行检查
        failed_critical = 0
        failed_non_critical = 0
        
        for check in self.checks:
            try:
                result = check['func']()
                if not result:
                    if check['critical']:
                        failed_critical += 1
                    else:
                        failed_non_critical += 1
            except Exception as e:
                logger.error(f"❌ 检查项异常 {check['name']}: {e}")
                if check['critical']:
                    failed_critical += 1
                else:
                    failed_non_critical += 1
        
        # 输出结果
        total_checks = len(self.checks)
        passed_checks = total_checks - failed_critical - failed_non_critical
        
        logger.info(f"📊 健康检查完成: {passed_checks}/{total_checks} 通过")
        
        if failed_critical > 0:
            logger.error(f"❌ {failed_critical} 个关键检查失败")
            return False
        
        if failed_non_critical > 0:
            logger.warning(f"⚠️ {failed_non_critical} 个非关键检查失败")
        
        logger.info("✅ 健康检查通过")
        return True

def main():
    """主函数"""
    try:
        checker = HealthChecker()
        
        # 运行健康检查
        is_healthy = checker.run_all_checks()
        
        if is_healthy:
            logger.info("🎉 系统健康状态良好")
            sys.exit(0)
        else:
            logger.error("💥 系统健康检查失败")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 健康检查程序异常: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()