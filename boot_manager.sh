#!/bin/bash

# 开机自启动管理脚本
# 支持多种启动方式：systemd、crontab、rc.local

PROJECT_DIR="/home/<USER>/Project-bot"
SERVICE_NAME="crypto-trading-bot"

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 检查是否为root用户
check_root() {
    if [ "$EUID" -eq 0 ]; then
        return 0
    else
        return 1
    fi
}

# 方法1: systemd服务（推荐）
setup_systemd() {
    echo -e "${BLUE}设置systemd服务开机自启...${NC}"
    
    if ! check_root; then
        echo -e "${RED}需要root权限，请使用sudo运行${NC}"
        return 1
    fi
    
    # 运行setup_service.sh
    if [ -f "$PROJECT_DIR/setup_service.sh" ]; then
        cd "$PROJECT_DIR"
        chmod +x setup_service.sh
        ./setup_service.sh
        echo -e "${GREEN}✓ systemd服务设置完成${NC}"
    else
        echo -e "${RED}setup_service.sh文件不存在${NC}"
        return 1
    fi
}

# 方法2: crontab（用户级）
setup_crontab() {
    echo -e "${BLUE}设置crontab开机自启...${NC}"
    
    cd "$PROJECT_DIR"
    chmod +x auto_restart_bot.sh
    
    # 备份现有crontab
    crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || true
    
    # 检查是否已存在相关条目
    if crontab -l 2>/dev/null | grep -q "auto_restart_bot.sh"; then
        echo -e "${YELLOW}crontab中已存在相关条目${NC}"
        echo "当前crontab内容："
        crontab -l | grep "auto_restart_bot.sh"
        read -p "是否要重新设置？(y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return 0
        fi
        
        # 删除现有条目
        crontab -l | grep -v "auto_restart_bot.sh" | crontab -
    fi
    
    # 添加新条目
    {
        crontab -l 2>/dev/null || true
        echo "@reboot sleep 60 && cd $PROJECT_DIR && ./auto_restart_bot.sh auto >> startup.log 2>&1"
        echo "0 */4 * * * cd $PROJECT_DIR && ./auto_restart_bot.sh restart >> cron_restart.log 2>&1"
    } | crontab -
    
    echo -e "${GREEN}✓ crontab开机自启设置完成${NC}"
}

# 方法3: rc.local（系统级）
setup_rclocal() {
    echo -e "${BLUE}设置rc.local开机自启...${NC}"
    
    if ! check_root; then
        echo -e "${RED}需要root权限，请使用sudo运行${NC}"
        return 1
    fi
    
    # 确保rc.local存在且可执行
    if [ ! -f /etc/rc.local ]; then
        cat > /etc/rc.local << 'EOF'
#!/bin/bash
# rc.local
#
# This script is executed at the end of each multiuser runlevel.
# Make sure that the script will "exit 0" on success or any other
# value on error.

exit 0
EOF
        chmod +x /etc/rc.local
    fi
    
    # 检查是否已存在条目
    if grep -q "auto_restart_bot.sh" /etc/rc.local; then
        echo -e "${YELLOW}rc.local中已存在相关条目${NC}"
        grep "auto_restart_bot.sh" /etc/rc.local
        read -p "是否要重新设置？(y/n): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            return 0
        fi
        
        # 删除现有条目
        sed -i '/auto_restart_bot.sh/d' /etc/rc.local
    fi
    
    # 在exit 0之前添加启动命令
    sed -i '/^exit 0/i\# Start crypto trading bot\nsudo -u ubuntu bash -c "cd /home/<USER>/Project-bot && ./auto_restart_bot.sh auto" > /var/log/crypto-bot-startup.log 2>&1 &' /etc/rc.local
    
    echo -e "${GREEN}✓ rc.local开机自启设置完成${NC}"
}

# 检查当前启动状态
check_status() {
    echo -e "${BLUE}检查开机自启状态...${NC}"
    echo ""
    
    # 检查systemd服务
    if systemctl list-unit-files | grep -q "$SERVICE_NAME"; then
        local status=$(systemctl is-enabled $SERVICE_NAME 2>/dev/null || echo "disabled")
        echo -e "systemd服务: ${GREEN}已安装${NC} (状态: $status)"
        if systemctl is-active --quiet $SERVICE_NAME; then
            echo -e "  当前状态: ${GREEN}运行中${NC}"
        else
            echo -e "  当前状态: ${RED}未运行${NC}"
        fi
    else
        echo -e "systemd服务: ${RED}未安装${NC}"
    fi
    
    # 检查crontab
    if crontab -l 2>/dev/null | grep -q "auto_restart_bot.sh"; then
        echo -e "crontab任务: ${GREEN}已设置${NC}"
        echo "  相关条目:"
        crontab -l | grep "auto_restart_bot.sh" | sed 's/^/    /'
    else
        echo -e "crontab任务: ${RED}未设置${NC}"
    fi
    
    # 检查rc.local
    if [ -f /etc/rc.local ] && grep -q "auto_restart_bot.sh" /etc/rc.local; then
        echo -e "rc.local启动: ${GREEN}已设置${NC}"
    else
        echo -e "rc.local启动: ${RED}未设置${NC}"
    fi
    
    # 检查进程状态
    echo ""
    cd "$PROJECT_DIR"
    if [ -f "bot.pid" ] && ps -p "$(cat bot.pid)" > /dev/null 2>&1; then
        echo -e "机器人进程: ${GREEN}运行中${NC} (PID: $(cat bot.pid))"
    else
        echo -e "机器人进程: ${RED}未运行${NC}"
    fi
}

# 清理所有开机自启设置
cleanup_all() {
    echo -e "${YELLOW}清理所有开机自启设置...${NC}"
    
    # 停止并禁用systemd服务
    if systemctl list-unit-files | grep -q "$SERVICE_NAME"; then
        if check_root; then
            sudo systemctl stop $SERVICE_NAME 2>/dev/null || true
            sudo systemctl disable $SERVICE_NAME 2>/dev/null || true
            sudo systemctl stop ${SERVICE_NAME}-restart.timer 2>/dev/null || true
            sudo systemctl disable ${SERVICE_NAME}-restart.timer 2>/dev/null || true
            echo "✓ systemd服务已停止和禁用"
        else
            echo "! 需要root权限来清理systemd服务"
        fi
    fi
    
    # 清理crontab
    if crontab -l 2>/dev/null | grep -q "auto_restart_bot.sh"; then
        crontab -l | grep -v "auto_restart_bot.sh" | crontab -
        echo "✓ crontab条目已清理"
    fi
    
    # 清理rc.local
    if [ -f /etc/rc.local ] && grep -q "auto_restart_bot.sh" /etc/rc.local; then
        if check_root; then
            sudo sed -i '/auto_restart_bot.sh/d' /etc/rc.local
            sudo sed -i '/# Start crypto trading bot/d' /etc/rc.local
            echo "✓ rc.local条目已清理"
        else
            echo "! 需要root权限来清理rc.local"
        fi
    fi
    
    echo -e "${GREEN}清理完成${NC}"
}

# 显示帮助
show_help() {
    echo "开机自启动管理脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  systemd    设置systemd服务开机自启（推荐，需要sudo）"
    echo "  crontab    设置crontab开机自启（用户级）"
    echo "  rclocal    设置rc.local开机自启（需要sudo）"
    echo "  status     检查当前开机自启状态"
    echo "  cleanup    清理所有开机自启设置"
    echo "  help       显示此帮助信息"
    echo ""
    echo "推荐使用顺序:"
    echo "1. $0 systemd    # 最稳定的方式"
    echo "2. $0 crontab    # 如果不想用systemd"
    echo "3. $0 status     # 检查设置是否成功"
}

# 主程序
main() {
    case "${1:-help}" in
        "systemd")
            setup_systemd
            ;;
        "crontab")
            setup_crontab
            ;;
        "rclocal")
            setup_rclocal
            ;;
        "status")
            check_status
            ;;
        "cleanup")
            cleanup_all
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 运行主程序
main "$@"