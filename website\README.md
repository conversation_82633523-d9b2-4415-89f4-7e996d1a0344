# 加密货币行情网站

一个现代化的加密货币行情展示网站，基于你现有的交易机器人数据构建。

## 功能特性

- 🚀 **实时行情数据** - 基于现有的币安API数据缓存
- 📊 **市场概览** - 总市值、交易量、涨跌统计
- 📈 **涨跌幅榜** - 实时涨幅和跌幅排行榜
- 🔍 **搜索功能** - 快速搜索特定加密货币
- 📱 **响应式设计** - 完美适配桌面端和移动端
- ⚡ **实时更新** - WebSocket实时数据推送
- 🎨 **现代化UI** - 基于Tailwind CSS的精美界面

## 技术栈

### 后端
- **FastAPI** - 现代化的Python Web框架
- **WebSocket** - 实时数据推送
- **Uvicorn** - ASGI服务器

### 前端
- **React 18** - 现代化的前端框架
- **TypeScript** - 类型安全的JavaScript
- **Tailwind CSS** - 实用优先的CSS框架
- **React Router** - 客户端路由
- **Axios** - HTTP客户端
- **Lucide React** - 现代化图标库

## 项目结构

```
website/
├── backend/                 # 后端API服务
│   ├── main.py             # FastAPI应用主文件
│   └── requirements.txt    # Python依赖
├── frontend/               # 前端React应用
│   ├── public/            # 静态文件
│   ├── src/               # 源代码
│   │   ├── components/    # React组件
│   │   ├── services/      # API服务
│   │   ├── types/         # TypeScript类型定义
│   │   ├── App.tsx        # 主应用组件
│   │   └── index.tsx      # 应用入口
│   ├── package.json       # 前端依赖
│   └── tailwind.config.js # Tailwind配置
└── README.md              # 项目说明
```

## 快速开始

### 1. 安装后端依赖

```bash
cd website/backend
pip install -r requirements.txt
```

### 2. 启动后端服务

```bash
cd website/backend
python main.py
```

后端服务将在 `http://localhost:8000` 启动

### 3. 安装前端依赖

```bash
cd website/frontend
npm install
```

### 4. 启动前端开发服务器

```bash
cd website/frontend
npm start
```

前端应用将在 `http://localhost:3000` 启动