#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI点位功能 - Telegram交互处理器
处理用户查询请求和结果展示，集成基差数据分析
"""

import asyncio
import logging
import json
import os
import tempfile
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import ContextTypes, ConversationHandler
import time

# 导入自定义模块
from AI_coin_query_manager import AICoinQueryManager
from AI_kline_fetcher import AIKlineFetcher
from non_blocking_ai_handler import non_blocking_ai_handler

# 导入时间相关函数
from crypto_trading_bot import format_beijing_time, get_beijing_time

logger = logging.getLogger(__name__)

# 对话状态
SELECTING_COIN, SELECTING_INTERVAL = range(2)

class AITelegramHandler:
    """AI点位的Telegram交互处理器"""
    
    def __init__(self, coin_query_manager: AICoinQueryManager, payment_callbacks=None):
        """初始化Telegram处理器

        Args:
            coin_query_manager: AI点位管理器实例
            payment_callbacks: 支付相关回调函数字典
        """
        self.query_manager = coin_query_manager
        self.kline_fetcher = AIKlineFetcher()
        self._market_cap_cache = {}
        self._last_cache_update = None
        self._cache_update_interval = 60  # 每60秒更新一次缓存

        # 币种验证缓存
        self._supported_symbols_cache = None
        self._symbols_cache_time = 0
        self._symbols_cache_ttl = 1800  # 30分钟缓存

        # 支付回调函数
        self.payment_callbacks = payment_callbacks or {}

    def _format_symbol_name(self, symbol: str) -> str:
        """智能格式化币种名称，正确处理各种币种格式

        Args:
            symbol: 完整的币种符号，如 'BTCUSDT', '1000SHIBUSDT', '1000PEPEUSDT'

        Returns:
            格式化后的显示名称，如 'BTC', '1000SHIB', '1000PEPE'
        """
        if not symbol:
            return symbol

        # 特殊币种映射规则
        special_mappings = {
            '1000SHIBUSDT': '1000SHIB',
            '1000PEPEUSDT': '1000PEPE',
            '1000BONKUSDT': '1000BONK',
            '1000FLOKIUSDT': '1000FLOKI',
            '1000LUNCUSDT': '1000LUNC',
            '1000SATSUSDT': '1000SATS',
            '1000000BOBUSDT': '1000000BOB',
            # 可以继续添加其他特殊币种
        }

        # 如果是特殊币种，直接返回映射结果
        if symbol in special_mappings:
            return special_mappings[symbol]

        # 如果以USDT结尾，移除USDT
        if symbol.endswith('USDT'):
            return symbol[:-4]  # 移除最后4个字符 'USDT'

        # 如果以BUSD结尾，移除BUSD
        if symbol.endswith('BUSD'):
            return symbol[:-4]  # 移除最后4个字符 'BUSD'

        # 其他情况直接返回
        return symbol

    def _escape_markdown(self, text: str) -> str:
        """转义Markdown特殊字符，防止解析错误"""
        if not text:
            return text

        # 转义Markdown特殊字符
        escape_chars = ['_', '*', '[', ']', '(', ')', '~', '`', '>', '#', '+', '-', '=', '|', '{', '}', '.', '!']
        for char in escape_chars:
            text = text.replace(char, f'\\{char}')

        return text

    async def _safe_edit_message(self, query, text: str, reply_markup=None, parse_mode='Markdown'):
        """安全地编辑消息，处理Markdown解析错误"""
        try:
            # 首先尝试使用Markdown模式
            await query.edit_message_text(
                text=text,
                reply_markup=reply_markup,
                parse_mode=parse_mode
            )
        except Exception as e:
            if "can't parse entities" in str(e).lower() or "can't find end of the entity" in str(e).lower():
                logger.warning(f"Markdown解析失败，尝试纯文本模式: {str(e)}")
                try:
                    # 如果Markdown解析失败，使用纯文本模式
                    await query.edit_message_text(
                        text=text,
                        reply_markup=reply_markup,
                        parse_mode=None
                    )
                except Exception as e2:
                    logger.error(f"纯文本模式也失败: {str(e2)}")
                    # 最后的备用方案：发送简化消息
                    await query.edit_message_text(
                        text="❌ 消息格式错误，请重试",
                        reply_markup=reply_markup,
                        parse_mode=None
                    )
            else:
                raise e
        
        logger.info("✅ AI Telegram处理器初始化完成")
    

    
    def _get_cached_supported_symbols(self) -> List[str]:
        """获取缓存的支持币种列表"""
        now = time.time()
        if (self._supported_symbols_cache is None or 
            (now - self._symbols_cache_time) > self._symbols_cache_ttl):
            
            logger.info("更新支持币种缓存...")
            all_symbols = self.query_manager.get_supported_symbols()
            
            # 验证每个币种是否真正支持，使用更轻量的验证方法
            validated_symbols = []
            for symbol in all_symbols:
                # 基本格式检查，避免重复调用get_supported_symbols
                if symbol and symbol.endswith('USDT') and len(symbol) > 4:
                    validated_symbols.append(symbol)
            
            self._supported_symbols_cache = validated_symbols
            self._symbols_cache_time = now
            logger.info(f"缓存了 {len(validated_symbols)} 个支持的币种")
        
        return self._supported_symbols_cache
    
    def _validate_symbol_fast(self, symbol: str) -> bool:
        """快速验证币种是否支持（使用缓存）"""
        if not symbol:
            return False

        # 基本格式检查
        if not symbol.endswith('USDT'):
            return False

        # 检查是否在缓存的支持列表中
        supported_symbols = self._get_cached_supported_symbols()
        return symbol.upper() in [s.upper() for s in supported_symbols]

    def _fuzzy_search_symbols(self, query: str, limit: int = 15) -> List[str]:
        """模糊搜索币种"""
        if not query:
            return []

        query = query.upper().strip()
        supported_symbols = self._get_cached_supported_symbols()

        # 搜索结果列表，按匹配度排序
        exact_matches = []      # 精确匹配
        start_matches = []      # 开头匹配
        contains_matches = []   # 包含匹配

        for symbol in supported_symbols:
            # 获取币种名称（去掉USDT）
            coin_name = self._format_symbol_name(symbol)

            if coin_name.upper() == query:
                exact_matches.append(symbol)
            elif coin_name.upper().startswith(query):
                start_matches.append(symbol)
            elif query in coin_name.upper():
                contains_matches.append(symbol)

        # 合并结果，按优先级排序
        results = exact_matches + start_matches + contains_matches

        # 去重并限制数量
        seen = set()
        unique_results = []
        for symbol in results:
            if symbol not in seen:
                seen.add(symbol)
                unique_results.append(symbol)
                if len(unique_results) >= limit:
                    break

        return unique_results

    async def _show_fuzzy_search_results(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                       query: str, search_results: List[str]) -> None:
        """显示模糊搜索结果"""
        try:
            if not search_results:
                await update.message.reply_text(
                    f"❌ 未找到与 '{query}' 相关的币种\n\n"
                    "💡 请尝试输入其他币种，如：BTC@、ETH@、SOL@等",
                    parse_mode='Markdown'
                )
                return

            # 创建币种按钮（每行5个）
            keyboard = []
            for i in range(0, len(search_results), 5):
                row = []
                for j in range(5):
                    if i + j < len(search_results):
                        symbol = search_results[i + j]
                        # 显示简化的币种名称
                        display_name = self._format_symbol_name(symbol)
                        row.append(InlineKeyboardButton(display_name, callback_data=f"coin_{symbol}"))
                keyboard.append(row)

            # 添加返回按钮
            keyboard.append([
                InlineKeyboardButton("🏠 返回主菜单", callback_data="cancel_analysis")
            ])

            reply_markup = InlineKeyboardMarkup(keyboard)

            # 格式化搜索结果文本
            result_names = [self._format_symbol_name(symbol) for symbol in search_results]
            results_text = "、".join(result_names)

            message_text = f"""🔍 找到与 '{query}' 相关的币种：

{results_text}

💡 点击下方按钮选择币种进行AI分析："""

            await update.message.reply_text(
                message_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        except Exception as e:
            logger.error(f"显示模糊搜索结果失败: {str(e)}")
            await update.message.reply_text(
                f"❌ 显示搜索结果时发生错误，请重试",
                parse_mode='Markdown'
            )
    
    def _load_market_cap_data(self) -> Dict[str, float]:
        """从缓存加载市值数据"""
        try:
            market_cap_data = {}
            
            # 首先尝试从现货流数据获取
            spot_cache_files = [
                'data/cache/coinglass_spot_flow_data.json',
                'data/cache/cache_data_primary.json',
                'data/cache/cache_data_secondary.json'
            ]
            
            for cache_file in spot_cache_files:
                if os.path.exists(cache_file):
                    try:
                        with open(cache_file, 'r', encoding='utf-8') as f:
                            cache_data = json.load(f)
                        
                        # 处理现货流数据格式
                        if 'data' in cache_data and isinstance(cache_data['data'], list):
                            for item in cache_data['data']:
                                if 'symbol' in item and 'market_cap' in item:
                                    symbol = item['symbol'] + 'USDT' if not item['symbol'].endswith('USDT') else item['symbol']
                                    market_cap_data[symbol] = float(item['market_cap'])
                        
                        # 处理CoinGlass市场数据格式
                        elif 'coinglass_market_cap_data' in cache_data:
                            data = cache_data['coinglass_market_cap_data']
                            if 'data' in data and isinstance(data['data'], list):
                                for item in data['data']:
                                    if 'symbol' in item and 'market_cap_usd' in item:
                                        symbol = item['symbol'] + 'USDT' if not item['symbol'].endswith('USDT') else item['symbol']
                                        market_cap_data[symbol] = float(item['market_cap_usd'])
                        
                        logger.info(f"从 {cache_file} 加载了 {len(market_cap_data)} 个币种的市值数据")
                        if market_cap_data:  # 如果获取到数据就使用第一个有效的文件
                            break
                            
                    except Exception as e:
                        logger.warning(f"读取缓存文件 {cache_file} 失败: {str(e)}")
                        continue
            
            return market_cap_data
            
        except Exception as e:
            logger.error(f"加载市值数据失败: {str(e)}")
            return {}
    
    def _get_top_symbols_by_market_cap(self, limit: int = 20) -> List[str]:
        """获取按市值排序的前N个币种"""
        try:
            # 检查是否需要更新缓存
            now = datetime.now()
            if (self._last_cache_update is None or 
                now - self._last_cache_update > timedelta(seconds=self._cache_update_interval)):
                
                self._market_cap_cache = self._load_market_cap_data()
                self._last_cache_update = now
                logger.info(f"更新市值缓存，共 {len(self._market_cap_cache)} 个币种")
            
            # 如果缓存为空，使用默认列表
            if not self._market_cap_cache:
                logger.warning("市值缓存为空，使用默认币种列表")
                return [
                    'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT', 'DOGEUSDT', 'ADAUSDT', 'TRXUSDT', 'AVAXUSDT', 'LINKUSDT',
                    'DOTUSDT', 'UNIUSDT', 'LTCUSDT', 'BCHUSDT', 'NEARUSDT', 'APTUSDT', 'FILUSDT', 'ARBUSDT', 'OPUSDT', 'TONUSDT'
                ]
            
            # 按市值排序并返回前N个
            sorted_symbols = sorted(self._market_cap_cache.items(), key=lambda x: x[1], reverse=True)
            top_symbols = [symbol for symbol, _ in sorted_symbols[:limit]]
            
            logger.info(f"按市值排序获取前{limit}个币种: {top_symbols[:5]}...")
            return top_symbols
            
        except Exception as e:
            logger.error(f"获取市值排序币种失败: {str(e)}")
            # 返回默认列表
            return [
                'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT', 'DOGEUSDT', 'ADAUSDT', 'TRXUSDT', 'AVAXUSDT', 'LINKUSDT',
                'DOTUSDT', 'UNIUSDT', 'LTCUSDT', 'BCHUSDT', 'NEARUSDT', 'APTUSDT', 'FILUSDT', 'ARBUSDT', 'OPUSDT', 'TONUSDT'
            ]
    
    async def start_coin_analysis(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """开始币种分析对话"""
        try:
            user_id = update.effective_user.id
            logger.info(f"用户 {user_id} 开始币种分析")
            

            
            # 按市值排序的前20个币种
            top_20_symbols = self._get_top_symbols_by_market_cap(20)
            
            # 过滤掉不支持的币种（使用快速验证）
            supported_symbols = []
            for symbol in top_20_symbols:
                if self._validate_symbol_fast(symbol):
                    supported_symbols.append(symbol)
                else:
                    logger.warning(f"过滤掉不支持的币种: {symbol}")
            
            # 如果支持的币种少于20个，补充一些默认的热门币种
            if len(supported_symbols) < 20:
                default_hot_symbols = [
                    'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'XRPUSDT', 'BNBUSDT', 
                    'DOGEUSDT', 'ADAUSDT', 'AVAXUSDT', 'LINKUSDT', 'DOTUSDT',
                    'UNIUSDT', 'LTCUSDT', 'BCHUSDT', 'NEARUSDT', 'FILUSDT',
                    'ARBUSDT', 'OPUSDT', 'MATICUSDT', 'APTUSDT', 'SUIUSDT'
                ]
                
                for symbol in default_hot_symbols:
                    if symbol not in supported_symbols and self._validate_symbol_fast(symbol):
                        supported_symbols.append(symbol)
                        if len(supported_symbols) >= 20:
                            break
            
            # 创建币种选择按钮（每行5个）
            keyboard = []
            for i in range(0, len(supported_symbols), 5):
                row = []
                for j in range(5):
                    if i + j < len(supported_symbols):
                        symbol = supported_symbols[i + j]
                        # 显示简化的币种名称（智能处理）
                        display_name = self._format_symbol_name(symbol)
                        row.append(InlineKeyboardButton(display_name, callback_data=f"coin_{symbol}"))
                keyboard.append(row)
            
            # 添加更多选项和返回按钮
            keyboard.append([
                InlineKeyboardButton("🔍 选择更多币种", callback_data="manual_input"),
                InlineKeyboardButton("🏠 返回主菜单", callback_data="cancel_analysis")
            ])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            message_text = f"""🎲 AI买卖点 - 算法加持，精准狙击

请选择要分析的币种（按市值排序）：

💡 功能特色：
- 🤖 自研300B加密货币大模型
- 📊 实时K线数据分析
- 🔧 123+技术指标计算
- 🚦 交易信号生成

📊 市值排序：显示当前市值最高的20个币种
🔄 实时更新：纳秒级自动更新排序

👇 点击下方按钮选择币种，或输入 币种@ 快速查询（如：BTC@）："""

            if update.callback_query:
                try:
                    await update.callback_query.edit_message_text(
                        text=message_text,
                        reply_markup=reply_markup,
                        parse_mode='Markdown'
                    )
                except Exception as e:
                    if "Message is not modified" in str(e):
                        # 如果消息内容相同，添加一个时间戳或其他标识使其不同
                        message_text_with_timestamp = message_text + f"\n\n⏰ 更新时间: {int(time.time()) % 1000}"
                        await update.callback_query.edit_message_text(
                            text=message_text_with_timestamp,
                            reply_markup=reply_markup,
                            parse_mode='Markdown'
                        )
                    else:
                        raise e
            else:
                await update.message.reply_text(
                    text=message_text,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
            
            return SELECTING_COIN
            
        except Exception as e:
            logger.error(f"开始币种分析失败: {str(e)}")
            await self._send_error_message(update, "启动分析功能时发生错误，请重试")
            return ConversationHandler.END
    
    async def handle_coin_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """处理币种选择"""
        try:
            query = update.callback_query
            await query.answer()
            
            if query.data == "cancel_analysis":
                # 返回主菜单 - 使用统一的处理逻辑
                return await self._handle_cancel_analysis(update, context)
            
            elif query.data == "manual_input":
                # 直接显示所有支持的币种
                return await self._show_all_supported_symbols(update, context)
            

            
            elif query.data == "show_all_symbols":
                return await self._show_all_supported_symbols(update, context)
            
            elif query.data == "symbols_prev_page":
                current_page = context.user_data.get('symbols_page', 0)
                context.user_data['symbols_page'] = max(0, current_page - 1)
                return await self._show_all_supported_symbols(update, context)
            
            elif query.data == "symbols_next_page":
                current_page = context.user_data.get('symbols_page', 0)
                context.user_data['symbols_page'] = current_page + 1
                return await self._show_all_supported_symbols(update, context)
            

            
            elif query.data == "back_to_coin_selection":
                # 重置分页状态并返回AI点位界面
                context.user_data['symbols_page'] = 0
                
                try:
                    return await self.start_coin_analysis(update, context)
                except Exception as e:
                    if "Message is not modified" in str(e):
                        # 如果消息内容相同，直接返回状态，不编辑消息
                        logger.info("消息内容相同，跳过编辑")
                        return SELECTING_COIN
                    else:
                        # 其他错误，重新抛出
                        raise e
            
            elif query.data.startswith("coin_"):
                selected_symbol = query.data.replace("coin_", "")
                context.user_data['selected_symbol'] = selected_symbol
                
                # 如果是直接从entry_points进入的，需要先验证币种
                if not self._validate_symbol_fast(selected_symbol):
                    await query.edit_message_text(
                        f"❌ 不支持的币种: {selected_symbol}\n\n"
                        "请重新选择支持的币种",
                    )
                    return ConversationHandler.END
                
                logger.info(f"用户选择币种: {selected_symbol}")
                return await self._show_interval_selection(update, context, selected_symbol)
                
        except Exception as e:
            logger.error(f"处理币种选择失败: {str(e)}")
            
            # 尝试发送错误消息而不结束对话
            try:
                if update.callback_query:
                    await update.callback_query.edit_message_text(
                        f"❌ 处理请求时发生错误: {str(e)}\n\n"
                        "请稍后重试或选择其他选项",
                        reply_markup=InlineKeyboardMarkup([
                            [InlineKeyboardButton("🔄 重新开始", callback_data="back_to_coin_selection")],
                            [InlineKeyboardButton("🏠 返回主菜单", callback_data="cancel_analysis")]
                        ])
                    )
                else:
                    await update.message.reply_text(
                        f"❌ 处理请求时发生错误: {str(e)}\n\n"
                        "请稍后重试"
                    )
                
                # 返回当前状态而不是结束对话，避免界面消失
                return SELECTING_COIN
                
            except Exception as send_error:
                logger.error(f"发送错误消息失败: {str(send_error)}")
                # 最后的备用方案，结束对话
                return ConversationHandler.END
    

    async def _show_all_supported_symbols(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """显示所有支持的币种列表"""
        try:
            # 使用缓存的支持币种列表（已验证过）
            validated_symbols = self._get_cached_supported_symbols()
            logger.info(f"使用缓存的支持币种列表，共 {len(validated_symbols)} 个币种")
            
            # 将币种按字母顺序排序并格式化显示
            symbol_names = [self._format_symbol_name(symbol) for symbol in validated_symbols]
            symbol_names.sort()
            
            # 分页显示，每页显示50个币种
            page_size = 50
            total_pages = (len(symbol_names) + page_size - 1) // page_size
            current_page = context.user_data.get('symbols_page', 0)
            
            # 确保页码不超出范围
            if current_page >= total_pages:
                current_page = total_pages - 1
                context.user_data['symbols_page'] = current_page
            
            start_idx = current_page * page_size
            end_idx = min(start_idx + page_size, len(symbol_names))
            page_symbols = symbol_names[start_idx:end_idx]
            
            # 创建币种按钮（每行5个）
            symbol_buttons = []
            for i in range(0, len(page_symbols), 5):
                row_symbols = page_symbols[i:i+5]
                button_row = []
                for symbol in row_symbols:
                    button_row.append(InlineKeyboardButton(symbol, callback_data=f"coin_{symbol}USDT"))
                symbol_buttons.append(button_row)
            
            # 格式化币种列表文本显示 - 使用动态对齐格式
            symbol_rows = []
            for i in range(0, len(page_symbols), 3):
                row_symbols = page_symbols[i:i+3]
                # 补齐不足3个的行
                while len(row_symbols) < 3:
                    row_symbols.append("")
                symbol_rows.append(row_symbols)
            
            # 使用动态对齐格式化
            symbols_text = self.dynamic_align_format(symbol_rows)
            
            # 组装完整键盘：币种按钮 + 功能按钮
            keyboard = symbol_buttons.copy()
            
            # 第一行：分页按钮
            first_row = []
            
            # 根据分页情况添加导航按钮
            if total_pages > 1:  # 只有在多页时才显示分页按钮
                if current_page > 0:  # 不是第一页，显示上一页
                    first_row.append(InlineKeyboardButton("⬅️ 上一页", callback_data="symbols_prev_page"))
                if current_page < total_pages - 1:  # 不是最后一页，显示下一页
                    first_row.append(InlineKeyboardButton("➡️ 下一页", callback_data="symbols_next_page"))
            
            keyboard.append(first_row)
            
            # 第二行：返回按钮
            keyboard.append([
                InlineKeyboardButton("🔄 返回AI点位界面", callback_data="back_to_coin_selection"),
                InlineKeyboardButton("🏠 返回主菜单", callback_data="cancel_analysis")
            ])
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            message_text = f"""📋 支持的币种列表 (第{current_page + 1}/{total_pages}页)
总计：{len(validated_symbols)} 个币种
```
{symbols_text}
```
💡 使用说明：
- 点击币种名称直接选择分析
- 或返回AI点位界面选择热门币种
- 系统支持所有主流数字货币分析

📄 第{current_page + 1}页，共{total_pages}页 | 显示 {start_idx + 1}-{end_idx} / {len(symbol_names)}"""

            await update.callback_query.edit_message_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
            return SELECTING_COIN
            
        except Exception as e:
            logger.error(f"显示支持币种列表失败: {str(e)}")
            await self._send_error_message(update, "显示币种列表时发生错误")
            return SELECTING_COIN
    
    async def _show_interval_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str = None) -> int:
        """显示时间周期选择"""
        try:
            # 如果没有传递symbol，从context获取
            if symbol is None:
                symbol = context.user_data.get('selected_symbol', 'Unknown')
            
            keyboard = [
                [
                    InlineKeyboardButton("15m", callback_data="interval_15m"),
                    InlineKeyboardButton("1h", callback_data="interval_1h"),
                    InlineKeyboardButton("4h", callback_data="interval_4h"),
                    InlineKeyboardButton("24h", callback_data="interval_1d")
                ],
                [
                    InlineKeyboardButton("🔙 重新选择币种", callback_data="back_to_coin"),
                    InlineKeyboardButton("🏠 返回主菜单", callback_data="cancel_analysis")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            coin_name = self._format_symbol_name(symbol)
            message_text = f"""📊 {coin_name} 合约分析

已选择币种：{symbol}

请选择分析周期：

⚡ 15m - 超短线分析
📈 1h - 短线分析  
📊 4h - 中线分析
🔍 24h - 日线分析

💡 说明：
- 系统将分析合约数据，参考现货数据
- 包含技术指标、基差分析、AI智能分析
- 提供交易信号和风险评估

👇 选择合适的分析周期："""

            if hasattr(update, 'callback_query') and update.callback_query:
                await update.callback_query.edit_message_text(
                    text=message_text,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
            else:
                await update.message.reply_text(
                    text=message_text,
                    reply_markup=reply_markup,
                    parse_mode='Markdown'
                )
            
            return SELECTING_INTERVAL
            
        except Exception as e:
            logger.error(f"显示市场选择失败: {str(e)}")
            await self._send_error_message(update, "显示市场选择时发生错误")
            return ConversationHandler.END

    async def _show_interval_selection_direct(self, update: Update, context: ContextTypes.DEFAULT_TYPE, symbol: str) -> int:
        """直接显示时间周期选择（用于币种@查询）"""
        try:
            keyboard = [
                [
                    InlineKeyboardButton("15m", callback_data="interval_15m"),
                    InlineKeyboardButton("1h", callback_data="interval_1h"),
                    InlineKeyboardButton("4h", callback_data="interval_4h"),
                    InlineKeyboardButton("24h", callback_data="interval_1d")
                ],
                [
                    InlineKeyboardButton("🏠 返回主菜单", callback_data="cancel_analysis")
                ]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            coin_name = self._format_symbol_name(symbol)
            message_text = f"""📊 {coin_name} 合约分析

已选择币种：{symbol}

请选择分析周期：

⚡ 15m - 超短线分析
📈 1h - 短线分析
📊 4h - 中线分析
🔍 24h - 日线分析

💡 说明：
- 系统将分析合约数据，参考现货数据
- 包含技术指标、基差分析、AI智能分析
- 提供交易信号和风险评估

👇 选择合适的分析周期："""

            await update.message.reply_text(
                text=message_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

            return SELECTING_INTERVAL

        except Exception as e:
            logger.error(f"直接显示时间周期选择失败: {str(e)}")
            await update.message.reply_text(
                "❌ 显示时间周期选择时发生错误，请重试",
                parse_mode='Markdown'
            )
            return ConversationHandler.END


    async def handle_interval_selection(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """处理时间周期选择"""
        try:
            query = update.callback_query
            await query.answer()
            
            if query.data == "cancel_analysis":
                # 返回主菜单 - 使用统一的处理逻辑
                return await self._handle_cancel_analysis(update, context)
            
            elif query.data == "back_to_coin":
                return await self.start_coin_analysis(update, context)
            
            elif query.data.startswith("interval_"):
                interval = query.data.replace("interval_", "")
                context.user_data['selected_interval'] = interval
                context.user_data['selected_market'] = 'futures'  # 默认分析合约数据

                # 💰 在选择分析周期时进行扣费
                user_id = query.from_user.id
                symbol = context.user_data.get('selected_symbol')

                # 使用回调函数进行扣费
                if self.payment_callbacks:
                    try:
                        check_access_func = self.payment_callbacks.get('check_access')
                        deduct_cost_func = self.payment_callbacks.get('deduct_cost')

                        if check_access_func and deduct_cost_func:
                            # 检查权限
                            can_access, error_msg = check_access_func(user_id, "coin_search")
                            if not can_access:
                                await query.edit_message_text(
                                    f"❌ {error_msg}",
                                    reply_markup=InlineKeyboardMarkup([
                                        [InlineKeyboardButton("💳 立即充值", callback_data="show_recharge")],
                                        [InlineKeyboardButton("🏠 返回主菜单", callback_data="main_menu")]
                                    ]),
                                    parse_mode='Markdown'
                                )
                                return ConversationHandler.END

                            # 扣除积分
                            deduct_success = deduct_cost_func(user_id, "coin_search")
                            if not deduct_success:
                                await query.edit_message_text(
                                    "💎 积分扣除失败，请稍后重试",
                                    reply_markup=InlineKeyboardMarkup([[
                                        InlineKeyboardButton("🏠 返回主菜单", callback_data="main_menu")
                                    ]]),
                                    parse_mode='Markdown'
                                )
                                return ConversationHandler.END

                            # 扣费成功，开始AI分析
                            await query.edit_message_text(f"💰 已扣除20积分\n🤖 正在分析 {self._format_symbol_name(symbol)} ({interval})...")
                        else:
                            logger.warning("支付回调函数未正确配置")
                            await query.edit_message_text(f"🤖 正在分析 {self._format_symbol_name(symbol)} ({interval})...")
                    except Exception as payment_error:
                        logger.error(f"扣费处理失败: {payment_error}")
                        await query.edit_message_text(
                            "❌ 积分处理失败，请稍后重试",
                            reply_markup=InlineKeyboardMarkup([[
                                InlineKeyboardButton("🏠 返回主菜单", callback_data="main_menu")
                            ]]),
                            parse_mode='Markdown'
                        )
                        return ConversationHandler.END
                else:
                    # 没有配置支付回调，直接开始分析
                    logger.warning("未配置支付回调函数，跳过扣费")
                    await query.edit_message_text(f"🤖 正在分析 {symbol.replace('USDT', '')} ({interval})...")

                await self._execute_ai_analysis(update, context, symbol, 'futures', interval)
                return ConversationHandler.END
                
        except Exception as e:
            logger.error(f"处理周期选择失败: {str(e)}")
            await self._send_error_message(update, "选择周期时发生错误")
            return ConversationHandler.END
    


    
    async def _execute_ai_analysis(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                 symbol: str, market_type: str, interval: str):
        """执行AI分析 - 快速响应优化版本"""
        try:
            # 🚀 快速响应：立即更新进度消息
            coin_name = symbol.replace('USDT', '')
            await update.callback_query.edit_message_text(
                f"⚡ {coin_name} AI分析已启动\n🔄 正在获取数据...",
                parse_mode='Markdown'
            )
            
            # 🚀 使用非阻塞AI分析，允许用户继续使用其他功能
            try:
                user_id = update.callback_query.from_user.id
                
                # 启动非阻塞AI分析
                analysis_id = await non_blocking_ai_handler.start_ai_analysis(
                    user_id, symbol, market_type, interval, self, update.callback_query
                )
                
                if analysis_id:
                    logger.info(f"用户 {user_id} 启动非阻塞AI分析: {analysis_id}")
                
            except asyncio.TimeoutError:
                # 超时处理（现在应该很少发生）
                await update.callback_query.edit_message_text(
                    f"⏰ {coin_name} AI分析处理时间较长\n\n"
                    f"🔄 分析仍在后台进行中，请耐心等待\n"
                    f"💡 提示：大约需要3-5分钟完成",
                    parse_mode='Markdown'
                )
                logger.warning(f"AI分析处理时间较长: {symbol}")
                return
            
        except Exception as e:
            logger.error(f"执行AI分析失败: {str(e)}")
            try:
                # 🚀 快速错误处理
                error_display = str(e)[:100] + "..." if len(str(e)) > 100 else str(e)

                # 💰 分析失败时退还积分
                user_id = update.callback_query.from_user.id
                try:
                    if self.payment_callbacks:
                        refund_func = self.payment_callbacks.get('refund_points')
                        if refund_func:
                            refund_func(user_id, 20, "AI分析失败退款")
                            logger.info(f"AI分析失败，已退还用户 {user_id} 积分: 20")
                        else:
                            logger.warning("未配置积分退还回调函数")
                    else:
                        logger.warning("未配置支付回调函数，无法退还积分")
                except Exception as refund_error:
                    logger.error(f"退还积分失败: {str(refund_error)}")

                if "Message is not modified" in str(e):
                    # 如果是消息重复错误，直接发送新消息
                    await update.callback_query.message.reply_text(
                        f"❌ {coin_name} AI分析失败，已退还20积分\n{error_display}"
                    )
                else:
                    await update.callback_query.edit_message_text(
                        f"❌ {coin_name} AI分析异常，已退还20积分\n\n"
                        f"错误: {error_display}\n"
                        f"🔄 请稍后重试",
                        parse_mode='Markdown'
                    )
            except Exception as edit_error:
                logger.error(f"发送错误消息失败: {str(edit_error)}")
    
    async def _send_simple_analysis_result(self, query, result: Dict):
        """发送简化的分析结果"""
        try:
            if not result.get('success', False):
                await query.edit_message_text(
                    f"❌ AI分析失败: {result.get('error', '未知错误')}"
                )
                return
            
            data = result.get('data', {})
            summary = data.get('summary', {})
            
            # 基础信息
            symbol = result.get('symbol', 'Unknown')
            coin_name = symbol.replace('USDT', '')
            current_price = summary.get('current_price', 0)
            price_change = summary.get('price_change_percent', 0)
            risk_level = summary.get('risk_assessment', '未知')
            
            # 方向图标
            if price_change > 0:
                direction = "🟢 ↑"
                change_text = f"+{price_change:.6f}%"
            elif price_change < 0:
                direction = "🔴 ↓"
                change_text = f"{price_change:.6f}%"
            else:
                direction = "⚪ →"
                change_text = "0.00%"
            
            # 获取AI分析内容
            ai_analysis = data.get('ai_analysis', {})
            ai_content = ""
            if ai_analysis.get('available', False) and 'analysis' in ai_analysis:
                ai_content = ai_analysis['analysis']
            
            # 构建完整的聊天框AI分析报告
            if ai_content:
                # 获取技术指标
                indicators = data.get('technical_indicators', {})
                adx = indicators.get('adx', 0)
                if adx > 25:
                    trend_strength = "强"
                elif adx > 20:
                    trend_strength = "中"
                else:
                    trend_strength = "弱"
                
                # 构建完整的聊天框消息 - 包含头部信息和完整AI分析
                chat_message = f"🤖 {coin_name} 详细AI分析\n\n"
                
                # 添加基本价格信息头部
                chat_message += f"# {coin_name} 市场分析报告\n"
                
                # 检查是否来自缓存
                cache_indicator = ""
                if ai_analysis.get('from_cache', False):
                    cache_indicator = " | 🔄 缓存数据"
                
                chat_message += f"{format_beijing_time(get_beijing_time().isoformat(), '%Y-%m-%d %H:%M:%S')} | 周期: {result.get('interval', '15m')}{cache_indicator}\n\n"
                
                chat_message += f"### {symbol}\n"
                # 根据价格大小决定小数位数
                high_24h = summary.get('high_24h')
                low_24h = summary.get('low_24h')

                if current_price >= 1:
                    price_format = f"${current_price:.2f}"
                    high_format = f"${high_24h:.2f}" if high_24h is not None else "数据获取中"
                    low_format = f"${low_24h:.2f}" if low_24h is not None else "数据获取中"
                else:
                    price_format = f"${current_price:.4f}"
                    high_format = f"${high_24h:.4f}" if high_24h is not None else "数据获取中"
                    low_format = f"${low_24h:.4f}" if low_24h is not None else "数据获取中"
                
                chat_message += f"## {price_format}\n"
                chat_message += f"24h高点: {high_format}\n"
                chat_message += f"24h低点: {low_format}\n"
                chat_message += f"趋势强度: {trend_strength} (ADX: {adx:.1f})\n"
                chat_message += f"{direction} {change_text}\n\n"
                
                # 只显示AI分析内容的前1000字符
                if len(ai_content) > 1000:
                    chat_message += ai_content[:1000] + "\n\n..."
                    chat_message += f"\n\n📄 完整分析请查看下方文件"
                else:
                    chat_message += ai_content
                
                # 创建详细的txt文件
                detailed_content = f"{coin_name} AI分析报告\n\n"
                detailed_content += ai_content + "\n\n"
                
                # 检查AI回复中是否已包含官方链接，避免重复添加
                if "关注官推获取最新前沿资讯与财富密码" not in ai_content:
                    detailed_content += "---\n\n"
                    detailed_content += "关注官推获取最新前沿资讯与财富密码： https://x.com/Z2H_Web3\n"
                    detailed_content += "加入tg群一起交流： t.me/zero2hero\n\n"
                    detailed_content += "点击链接免费领取价值1万u的交易知识库： https://unruly-blender-9fd.notion.site/Zero-2-Hero-1fefb439dbc280b38aabebdfcb3a346f\n\n"
                    detailed_content += "免责声明：本分析仅供参考，不构成投资建议，投资有风险，入市需谨慎。\n\n"
                
                detailed_content += f"分析时间: {format_beijing_time(get_beijing_time().isoformat(), '%Y-%m-%d %H:%M:%S')}\n"
                
                # 保存到txt文件
                import tempfile
                import os
                filename = f"{coin_name}_AI分析详细报告_{format_beijing_time(get_beijing_time().isoformat(), '%Y%m%d_%H%M%S')}.txt"
                
                # 创建临时文件
                with tempfile.NamedTemporaryFile(mode='w', delete=False, suffix='.txt', encoding='utf-8') as tmp_file:
                    tmp_file.write(detailed_content)
                    temp_file_path = tmp_file.name
                
                # 先发送聊天框简洁版本，然后在同一条消息中发送文件
                await self._safe_edit_message(query, chat_message)
                
                # 发送文件
                try:
                    with open(temp_file_path, 'rb') as file:
                        await query.message.reply_document(
                            document=file,
                            filename=filename,
                            caption=f"📄 {coin_name} 完整AI分析报告"
                        )
                except Exception as file_error:
                    logger.error(f"发送文件失败: {file_error}")
                    await query.message.reply_text("📄 详细分析报告生成失败")
                finally:
                    # 清理临时文件
                    try:
                        os.unlink(temp_file_path)
                    except:
                        pass
                        
            else:
                # 如果没有AI分析内容，使用简化格式
                indicators = data.get('technical_indicators', {})
                
                # RSI信息
                rsi_value = indicators.get('rsi', 0)
                if rsi_value >= 70:
                    rsi_signal = "超买"
                elif rsi_value <= 30:
                    rsi_signal = "超卖"
                else:
                    rsi_signal = "正常"
                
                # 检查是否有AI分析错误信息
                ai_analysis = data.get('ai_analysis', {})
                ai_error = ai_analysis.get('error', '')
                
                # 构建简化报告
                report = f"🎯 {coin_name} AI分析报告\n\n"
                report += f"💰 当前价格： ${current_price:.4f}\n"
                report += f"📊 价格变化： {direction} {change_text}\n"
                report += f"⚠️ 风险等级： {risk_level}\n\n"
                report += f"🔧 关键技术指标\n"
                report += f"- RSI: {rsi_value:.1f} ({rsi_signal})\n\n"
                
                # 如果有具体的AI错误信息，显示友好的错误信息
                if ai_error and any(keyword in ai_error.lower() for keyword in ['timeout', '超时', '繁忙']):
                    report += f"🤖 AI分析系统繁忙\n\n"
                    report += f"⏰ 当前请求较多，建议稍后重试\n"
                    report += f"📊 您可以先参考上述技术指标数据"
                elif ai_error and any(keyword in ai_error.lower() for keyword in ['network', 'connection', '网络', '连接']):
                    report += f"🌐 AI分析网络异常\n\n"
                    report += f"📡 网络连接不稳定，建议稍后重试\n"
                    report += f"📊 您可以先参考上述技术指标数据"
                else:
                    report += f"📖 AI分析功能暂时不可用\n\n"
                    report += f"🔄 建议稍后重试或查看技术指标"
                
                await self._safe_edit_message(query, report)
                
        except Exception as e:
            logger.error(f"发送简化分析结果失败: {str(e)}")
            try:
                error_msg = f"❌ 发送分析结果时发生错误: {str(e)}"
                if "Message is not modified" in str(e):
                    # 如果是消息重复错误，直接发送新消息
                    await query.message.reply_text(error_msg)
                else:
                    await query.edit_message_text(error_msg)
            except Exception as edit_error:
                logger.error(f"发送错误消息失败: {str(edit_error)}")
    
    async def _execute_basic_analysis(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                    symbol: str, market_type: str, interval: str):
        """执行基础分析"""
        try:
            # 显示分析进度
            await update.callback_query.edit_message_text(
                "📊 正在进行基础分析，请稍候...",
                parse_mode='Markdown'
            )
            
            # 执行基础分析（不使用AI）- 移除超时限制
            result = await self.query_manager.analyze_coin(symbol, market_type, interval, use_ai=False)
            
            # 使用简化的结果发送
            await self._send_simple_analysis_result(update.callback_query, result)
            
        except Exception as e:
            logger.error(f"执行基础分析失败: {str(e)}")
            try:
                error_msg = f"❌ 基础分析失败: {str(e)}"
                if "Message is not modified" in str(e):
                    await update.callback_query.message.reply_text(error_msg)
                else:
                    await update.callback_query.edit_message_text(error_msg)
            except Exception as edit_error:
                logger.error(f"发送错误消息失败: {str(edit_error)}")
    
    async def _execute_basis_analysis(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                    symbol: str, market_type: str, interval: str):
        """执行基差分析"""
        try:
            await update.callback_query.edit_message_text(
                "📈 正在进行基差分析，请稍候...",
                parse_mode='Markdown'
            )
            
            # 获取基差数据
            basis_data = await self._fetch_basis_data(symbol, interval)
            
            if basis_data:
                # 分析基差数据并生成简化报告
                coin_name = symbol.replace('USDT', '')
                
                # 提取最新基差数据
                latest = basis_data[-1]
                current_basis = latest.get('close_basis', 0)
                
                # 计算基差统计
                basis_values = [item.get('close_basis', 0) for item in basis_data]
                avg_basis = sum(basis_values) / len(basis_values) if basis_values else 0
                
                # 判断市场情绪
                if current_basis > 0.1:
                    sentiment = "🐂 极度乐观"
                elif current_basis > 0.05:
                    sentiment = "📈 偏向乐观"
                elif current_basis > 0:
                    sentiment = "😐 轻微乐观"
                elif current_basis > -0.05:
                    sentiment = "😐 轻微悲观"
                elif current_basis > -0.1:
                    sentiment = "📉 偏向悲观"
                else:
                    sentiment = "🐻 极度悲观"
                
                # 构建简化的基差报告
                report = f"📈 {coin_name} 基差分析报告\n\n"
                report += f"🎯 当前基差：{current_basis:.4f}%\n"
                report += f"📊 市场情绪：{sentiment}\n"
                report += f"📊 平均基差：{avg_basis:.4f}%\n"
                report += f"📋 数据点数：{len(basis_data)}\n\n"
                
                if current_basis > 0:
                    report += f"💡 正基差：合约价格高于现货\n"
                    report += f"市场情绪偏向乐观\n"
                else:
                    report += f"💡 负基差：合约价格低于现货\n"
                    report += f"市场情绪偏向悲观\n"
                
                if abs(current_basis) > 0.1:
                    report += f"\n🔔 基差较大，可能存在套利机会"
                
                await update.callback_query.edit_message_text(report, parse_mode='Markdown')
            else:
                await update.callback_query.edit_message_text(
                    f"❌ 无法获取 {symbol.replace('USDT', '')} 的基差数据"
                )
            
        except Exception as e:
            logger.error(f"执行基差分析失败: {str(e)}")
            try:
                error_msg = f"❌ 基差分析失败: {str(e)}"
                if "Message is not modified" in str(e):
                    await update.callback_query.message.reply_text(error_msg)
                else:
                    await update.callback_query.edit_message_text(error_msg)
            except Exception as edit_error:
                logger.error(f"发送错误消息失败: {str(edit_error)}")
    
    async def _execute_batch_analysis(self, update: Update, context: ContextTypes.DEFAULT_TYPE,
                                    symbol: str, market_type: str, interval: str):
        """执行批量对比分析"""
        try:
            await update.callback_query.edit_message_text(
                "🔄 正在进行批量对比分析，请稍候...",
                parse_mode='Markdown'
            )
            
            # 选择对比币种
            compare_symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'BNBUSDT']
            if symbol not in compare_symbols:
                compare_symbols.append(symbol)
            
            # 执行批量分析
            batch_result = await self.query_manager.batch_analyze_coins(
                compare_symbols, market_type, interval, use_ai=False
            )
            
            # 生成简化的对比报告
            if batch_result.get('success', False):
                results = batch_result.get('results', {})
                
                report = f"🔄 批量对比分析报告\n\n"
                report += f"🎯 目标币种：{symbol.replace('USDT', '')}\n"
                report += f"📊 对比币种数：{len(results)}\n\n"
                
                # 排序币种按价格变化
                sorted_data = []
                for sym, data in results.items():
                    if data.get('success') and 'summary' in data.get('data', {}):
                        summary = data['data']['summary']
                        change_percent = summary.get('price_change_percent', 0)
                        price = summary.get('current_price', 0)
                        risk = summary.get('risk_assessment', '未知')
                        sorted_data.append((sym, change_percent, price, risk))
                
                sorted_data.sort(key=lambda x: x[1], reverse=True)
                
                report += "📈 涨跌幅排行\n"
                for i, (sym, change_percent, price, risk) in enumerate(sorted_data[:5]):
                    rank_emoji = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][i] if i < 5 else "-"
                    direction = "📈" if change_percent > 0 else "📉" if change_percent < 0 else "➡️"
                    coin_name = sym.replace('USDT', '')
                    report += f"{rank_emoji} {coin_name}: ${price:.2f} {direction} {change_percent:+.2f}%\n"
                
                await self._safe_edit_message(update.callback_query, report)
            else:
                await update.callback_query.edit_message_text(
                    f"❌ 批量分析失败: {batch_result.get('error', '未知错误')}"
                )
            
        except Exception as e:
            logger.error(f"执行批量分析失败: {str(e)}")
            try:
                error_msg = f"❌ 批量分析失败: {str(e)}"
                if "Message is not modified" in str(e):
                    await update.callback_query.message.reply_text(error_msg)
                else:
                    await update.callback_query.edit_message_text(error_msg)
            except Exception as edit_error:
                logger.error(f"发送错误消息失败: {str(edit_error)}")
    
    async def _fetch_basis_data(self, symbol: str, interval: str) -> Optional[List[Dict]]:
        """获取基差数据"""
        try:
            import requests
            
            url = "https://open-api-v4.coinglass.com/api/futures/basis/history"
            headers = {
                "accept": "application/json",
                "CG-API-KEY": "c40fbbee201d4dfab3a4b62f37f0b610"
            }
            params = {
                "exchange": "Binance",
                "symbol": symbol,
                "interval": interval,
                "limit": 100
            }
            
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                if data.get("code") == "0" and "data" in data:
                    return data["data"]
            
            return None
            
        except Exception as e:
            logger.error(f"获取基差数据失败: {str(e)}")
            return None
    
    def _analyze_basis_data(self, symbol: str, basis_data: List[Dict], interval: str) -> str:
        """分析基差数据并生成报告"""
        try:
            if not basis_data:
                return f"❌ {symbol} 基差数据为空"
            
            # 提取最新基差数据
            latest = basis_data[-1]
            current_basis = latest.get('close_basis', 0)
            
            # 计算基差统计
            basis_values = [item.get('close_basis', 0) for item in basis_data]
            avg_basis = sum(basis_values) / len(basis_values)
            max_basis = max(basis_values)
            min_basis = min(basis_values)
            
            # 判断市场情绪
            if current_basis > 0.1:
                sentiment = "🐂 极度乐观"
            elif current_basis > 0.05:
                sentiment = "📈 偏向乐观"
            elif current_basis > 0:
                sentiment = "😐 轻微乐观"
            elif current_basis > -0.05:
                sentiment = "😐 轻微悲观"
            elif current_basis > -0.1:
                sentiment = "📉 偏向悲观"
            else:
                sentiment = "🐻 极度悲观"
            
            # 分析基差趋势
            if len(basis_data) >= 2:
                prev_basis = basis_data[-2].get('close_basis', 0)
                basis_change = current_basis - prev_basis
                trend = "📈 上升" if basis_change > 0 else "📉 下降" if basis_change < 0 else "➡️ 平稳"
            else:
                trend = "➡️ 数据不足"
            
            report = f"""📈 {symbol} 基差分析报告

⏰ 分析周期： {interval}
📊 数据点数： {len(basis_data)}

📋 当前基差状况
🎯 当前基差：{current_basis:.4f}%
📊 市场情绪：{sentiment}
📈 基差趋势：{trend}

📈 基差统计
📊 平均基差：{avg_basis:.4f}%
📈 最大基差：{max_basis:.4f}%
📉 最小基差：{min_basis:.4f}%

💡 基差解读
"""
            
            if current_basis > 0:
                report += f"- 正基差({current_basis:.4f}%)：合约价格高于现货\n"
                report += "- 市场情绪偏向乐观，可能处于牛市\n"
                if current_basis > avg_basis:
                    report += "- 当前基差高于平均值，市场乐观情绪较强\n"
            else:
                report += f"- 负基差({current_basis:.4f}%)：合约价格低于现货\n"
                report += "- 市场情绪偏向悲观，或现货需求旺盛\n"
                if abs(current_basis) > abs(avg_basis):
                    report += "- 当前基差偏离平均值，市场情绪较为极端\n"
            
            # 套利机会提示
            if abs(current_basis) > 0.1:
                report += f"\n🔔 套利提示\n"
                report += f"- 基差较大({abs(current_basis):.4f}%)，可能存在期现套利机会\n"
                report += f"- 建议关注基差收敛的交易机会\n"
            
            # 风险提示
            report += f"\n⚠️ 风险提示\n"
            report += f"- 基差数据仅供参考，不构成投资建议\n"
            report += f"- 套利交易需要考虑资金费率和手续费\n"
            report += f"- 市场变化快，请谨慎操作\n"
            
            return report
            
        except Exception as e:
            logger.error(f"分析基差数据失败: {str(e)}")
            return f"❌ 基差数据分析失败: {str(e)}"
    
    def _generate_comparison_report(self, batch_result: Dict, target_symbol: str) -> str:
        """生成对比分析报告"""
        try:
            if not batch_result.get('success', False):
                return "❌ 批量分析失败"
            
            results = batch_result.get('results', {})
            
            report = f"🔄 批量对比分析报告\n\n"
            report += f"🎯 目标币种： {target_symbol}\n"
            report += f"📊 对比币种数： {len(results)}\n\n"
            
            # 排序币种按价格变化
            sorted_symbols = []
            for symbol, data in results.items():
                if data.get('success') and 'summary' in data.get('data', {}):
                    summary = data['data']['summary']
                    change_percent = summary.get('price_change_percent', 0)
                    sorted_symbols.append((symbol, change_percent, summary))
            
            sorted_symbols.sort(key=lambda x: x[1], reverse=True)
            
            report += "📈 涨跌幅排行\n"
            for i, (symbol, change_percent, summary) in enumerate(sorted_symbols[:5]):
                rank_emoji = ["🥇", "🥈", "🥉", "4️⃣", "5️⃣"][i]
                direction = "📈" if change_percent > 0 else "📉" if change_percent < 0 else "➡️"
                coin_name = symbol.replace('USDT', '')
                price = summary.get('current_price', 0)
                risk = summary.get('risk_assessment', '未知')
                
                report += f"{rank_emoji} {coin_name}: ${price:,.2f} {direction} {change_percent:+.2f}% (风险: {risk})\n"
            
            # 目标币种的相对表现
            if target_symbol in results:
                target_data = results[target_symbol]
                if target_data.get('success'):
                    target_summary = target_data['data']['summary']
                    target_change = target_summary.get('price_change_percent', 0)
                    
                    better_count = sum(1 for _, change, _ in sorted_symbols if change > target_change)
                    total_count = len(sorted_symbols)
                    
                    report += f"\n🎯 {target_symbol.replace('USDT', '')} 相对表现\n"
                    report += f"- 排名：{better_count + 1}/{total_count}\n"
                    report += f"- 超越了 {total_count - better_count - 1} 个币种\n"
                    
                    if better_count < total_count // 3:
                        report += "- 🏆 表现优秀，位于前1/3\n"
                    elif better_count < total_count * 2 // 3:
                        report += "- 📊 表现中等，位于中间1/3\n"
                    else:
                        report += "- 📉 表现较弱，位于后1/3\n"
            
            return report
            
        except Exception as e:
            logger.error(f"生成对比报告失败: {str(e)}")
            return f"❌ 生成对比报告失败: {str(e)}"
    
    async def _send_analysis_result(self, query, result: Dict, analysis_type: str):
        """发送分析结果 - 支持内联键盘分页"""
        try:
            if not result.get('success', False):
                await query.edit_message_text(
                    f"❌ {analysis_type}失败: {result.get('error', '未知错误')}"
                )
                return

            data = result.get('data', {})
            summary = data.get('summary', {})
            
            # 基础信息
            symbol = result.get('symbol', 'Unknown')
            coin_name = symbol.replace('USDT', '')
            current_price = summary.get('current_price', 0)
            price_change = summary.get('price_change_percent', 0)
            risk_level = summary.get('risk_assessment', '未知')
            
            direction = "📈" if price_change > 0 else "📉" if price_change < 0 else "➡️"
            
            # 获取AI分析内容
            ai_analysis = data.get('ai_analysis', {})
            ai_content = ""
            if ai_analysis.get('available', False) and 'analysis' in ai_analysis:
                ai_content = ai_analysis['analysis']
            
            # 准备分页数据
            page_data = self._prepare_analysis_pages(coin_name, analysis_type, current_price, price_change, direction, risk_level, data, ai_content, summary)
            
            # 将分页数据存储到用户上下文中
            user_id = query.from_user.id
            if not hasattr(query, 'message') or not hasattr(query.message, 'chat'):
                # 如果是callback query，获取chat_id
                chat_id = query.message.chat.id if query.message else user_id
            else:
                chat_id = query.message.chat.id
            
            # 在全局存储分页数据（实际应用中可能需要使用数据库）
            if not hasattr(self, '_pagination_data'):
                self._pagination_data = {}
            
            pagination_key = f"{user_id}_{chat_id}_{symbol}"
            self._pagination_data[pagination_key] = {
                'pages': page_data,
                'current_page': 0,
                'total_pages': len(page_data),
                'symbol': symbol,
                'timestamp': time.time()
            }
            
            # 发送第一页
            await self._send_paginated_message(query, pagination_key, 0)
                
        except Exception as e:
            logger.error(f"发送分析结果失败: {str(e)}")
            await query.edit_message_text(f"❌ 发送分析结果时发生错误: {str(e)}")

    def _prepare_analysis_pages(self, coin_name: str, analysis_type: str, current_price: float,
                               price_change: float, direction: str, risk_level: str, 
                               data: Dict, ai_content: str, summary: Dict) -> List[str]:
        """准备分析页面数据"""
        pages = []
        
        # 第一页：基础分析和交易策略
        page1 = self._build_page1_analysis(coin_name, analysis_type, current_price, price_change, direction, risk_level, data, ai_content)
        pages.append(page1)
        
        # 第二页：详细技术指标分析
        if ai_content:
            page2 = self._build_page2_technical_details(coin_name, ai_content)
            pages.append(page2)
        
        return pages

    def _build_page1_analysis(self, coin_name: str, analysis_type: str, current_price: float,
                             price_change: float, direction: str, risk_level: str, 
                             data: Dict, ai_content: str) -> str:
        """构建第一页：基础分析"""
        # 从AI分析内容中提取第一页内容
        if ai_content and "🤖 AI分析续页" in ai_content:
            # 分离第一页和第二页内容
            parts = ai_content.split("🤖 AI分析续页")
            first_page_content = parts[0].strip()
        else:
            # 如果没有分页标识，取前3500字符作为第一页
            first_page_content = ai_content[:3500] if ai_content else ""
        
        if first_page_content:
            return first_page_content
        else:
            # 如果没有AI内容，构建基础摘要页面
            return self._build_summary_page(coin_name, analysis_type, current_price, price_change, direction, risk_level, data)

    def _build_page2_technical_details(self, coin_name: str, ai_content: str) -> str:
        """构建第二页：详细技术指标"""
        if "🤖 AI分析续页" in ai_content:
            # 提取第二页内容
            parts = ai_content.split("🤖 AI分析续页")
            if len(parts) > 1:
                return f"🤖 AI分析续页\n\n{parts[1].strip()}"
        
        # 如果没有分页标识，提取技术指标相关内容
        if "多周期技术指标详细分析" in ai_content:
            tech_start = ai_content.find("多周期技术指标详细分析")
            return ai_content[tech_start:]
        
        # 默认返回后半部分内容
        if len(ai_content) > 3500:
            return f"🤖 {coin_name} 技术指标续页\n\n{ai_content[3500:]}"
        
        return f"🤖 {coin_name} 技术指标分析\n\n暂无详细技术指标数据"

    async def _send_paginated_message(self, query, pagination_key: str, page_index: int):
        """发送分页消息"""
        try:
            if pagination_key not in self._pagination_data:
                await query.edit_message_text("❌ 分页数据已过期，请重新查询")
                return
            
            page_info = self._pagination_data[pagination_key]
            pages = page_info['pages']
            total_pages = page_info['total_pages']
            
            if page_index < 0 or page_index >= total_pages:
                return
            
            # 构建当前页内容
            current_page_content = pages[page_index]
            
            # 构建内联键盘
            keyboard = []
            nav_buttons = []
            
            # 上一页按钮
            if page_index > 0:
                nav_buttons.append(InlineKeyboardButton("⬅️ 上一页", callback_data=f"page_{pagination_key}_{page_index-1}"))
            
            # 页面指示器
            nav_buttons.append(InlineKeyboardButton(f"{page_index+1}/{total_pages}", callback_data="page_info"))
            
            # 下一页按钮
            if page_index < total_pages - 1:
                nav_buttons.append(InlineKeyboardButton("下一页 ➡️", callback_data=f"page_{pagination_key}_{page_index+1}"))
            
            if nav_buttons:
                keyboard.append(nav_buttons)
            
            # 功能按钮
            function_buttons = [
                InlineKeyboardButton("🔄 重新分析", callback_data=f"reanalyze_{page_info['symbol']}"),
                InlineKeyboardButton("🏠 返回主菜单", callback_data="cancel_analysis")
            ]
            keyboard.append(function_buttons)
            
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            # 更新当前页
            self._pagination_data[pagination_key]['current_page'] = page_index
            
            # 发送或编辑消息
            await query.edit_message_text(
                text=current_page_content,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )
            
        except Exception as e:
            logger.error(f"发送分页消息失败: {str(e)}")
            await query.edit_message_text(f"❌ 显示页面时发生错误: {str(e)}")

    async def handle_pagination_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """处理分页回调"""
        try:
            query = update.callback_query
            await query.answer()
            
            callback_data = query.data
            
            if callback_data.startswith("page_"):
                parts = callback_data.split("_")
                if len(parts) >= 4:
                    # page_userid_chatid_symbol_pageindex
                    pagination_key = "_".join(parts[1:-1])
                    page_index = int(parts[-1])
                    
                    await self._send_paginated_message(query, pagination_key, page_index)
            
            elif callback_data == "page_info":
                # 页面信息按钮，不做任何操作
                pass
            
            elif callback_data.startswith("reanalyze_"):
                # 重新分析
                symbol = callback_data.replace("reanalyze_", "")
                await query.edit_message_text(f"🔄 正在重新分析 {symbol}，请稍候...")
                # 这里可以调用重新分析的逻辑
                
        except Exception as e:
            logger.error(f"处理分页回调失败: {str(e)}")
            await query.answer("❌ 操作失败，请重试", show_alert=True)
    
    def _build_summary_page(self, coin_name: str, analysis_type: str, current_price: float, 
                           price_change: float, direction: str, risk_level: str, data: Dict) -> str:
        """构建摘要页面"""
        page = f"🎯 {coin_name} {analysis_type}报告\n\n"
        page += f"💰 当前价格： ${current_price:,.4f}\n"
        page += f"📊 价格变化： {direction} {price_change:+.2f}%\n"
        page += f"⚠️ 风险等级： {risk_level}\n\n"
        
        # 技术指标摘要
        indicators = data.get('technical_indicators', {})
        if indicators:
            page += "🔧 关键技术指标\n"
            
            if 'rsi' in indicators:
                rsi = indicators['rsi']
                rsi_signal = indicators.get('rsi_signal', '')
                page += f"- RSI: {rsi:.1f} ({rsi_signal})\n"
            
            if 'ma_20_diff_percent' in indicators:
                ma_diff = indicators['ma_20_diff_percent']
                ma_direction = "上方" if ma_diff > 0 else "下方"
                page += f"- MA20: 价格在{ma_direction} {abs(ma_diff):.1f}%\n"
            
            if 'bb_signal' in indicators:
                page += f"- 布林带: {indicators['bb_signal']}\n"
            
            if 'volume_signal' in indicators:
                page += f"- 成交量: {indicators['volume_signal']}\n"
        
        # 交易信号
        signals = data.get('trading_signals', {})
        if signals:
            page += f"\n🚦 交易信号\n"
            if 'overall_signal' in signals:
                page += f"- 综合信号: {signals['overall_signal']}\n"
            if 'signal_strength' in signals:
                page += f"- 信号强度: {signals['signal_strength']}\n"
        
        page += f"\n📖 详细AI分析将在下一条消息中显示..."
        return page
    
    def _build_ai_analysis_page(self, coin_name: str, ai_content: str, summary: Dict) -> str:
        """构建AI分析页面"""
        page = f"🤖 {coin_name} AI分析报告\n\n"
        page += ai_content
        
        # 添加数据质量信息
        data_quality = summary.get('data_quality', {})
        if data_quality:
            page += f"\n\n📊 数据质量\n"
            page += f"- K线数据: {data_quality.get('kline_points', 0)} 根\n"
            page += f"- 技术指标: {data_quality.get('indicators_count', 0)} 个\n"
        
        page += f"\n⏰ 分析时间: {format_beijing_time(get_beijing_time().isoformat(), '%Y-%m-%d %H:%M:%S')}"
        return page
    
    def _split_ai_analysis(self, ai_content: str) -> List[str]:
        """智能分割AI分析内容"""
        # 按段落和关键分隔符分割
        parts = []
        current_part = ""
        
        # 按行分割
        lines = ai_content.split('\n')
        
        for line in lines:
            # 如果当前部分加上新行会超过3500字符，就开始新的部分
            if len(current_part) + len(line) > 3500:
                if current_part:
                    parts.append(current_part.strip())
                current_part = line + '\n'
            else:
                current_part += line + '\n'
        
        # 添加最后一部分
        if current_part.strip():
            parts.append(current_part.strip())
        
        return parts if parts else [ai_content]
    
    def _build_complete_report(self, coin_name: str, analysis_type: str, current_price: float,
                             price_change: float, direction: str, risk_level: str, 
                             data: Dict, summary: Dict) -> str:
        """构建完整报告（无AI分析时使用）"""
        report = f"🎯 {coin_name} {analysis_type}报告\n\n"
        report += f"💰 当前价格： ${current_price:,.4f}\n"
        report += f"📊 价格变化： {direction} {price_change:+.2f}%\n"
        report += f"⚠️ 风险等级： {risk_level}\n\n"
        
        # 技术指标摘要
        indicators = data.get('technical_indicators', {})
        if indicators:
            report += "🔧 关键技术指标\n"
            
            if 'rsi' in indicators:
                rsi = indicators['rsi']
                rsi_signal = indicators.get('rsi_signal', '')
                report += f"- RSI: {rsi:.1f} ({rsi_signal})\n"
            
            if 'ma_20_diff_percent' in indicators:
                ma_diff = indicators['ma_20_diff_percent']
                ma_direction = "上方" if ma_diff > 0 else "下方"
                report += f"- MA20: 价格在{ma_direction} {abs(ma_diff):.1f}%\n"
            
            if 'bb_signal' in indicators:
                report += f"- 布林带: {indicators['bb_signal']}\n"
            
            if 'volume_signal' in indicators:
                report += f"- 成交量: {indicators['volume_signal']}\n"
        
        # 交易信号
        signals = data.get('trading_signals', {})
        if signals:
            report += f"\n🚦 交易信号\n"
            if 'overall_signal' in signals:
                report += f"- 综合信号: {signals['overall_signal']}\n"
            if 'signal_strength' in signals:
                report += f"- 信号强度: {signals['signal_strength']}\n"
        
        # 数据质量信息
        data_quality = summary.get('data_quality', {})
        if data_quality:
            report += f"\n📊 数据质量\n"
            report += f"- K线数据: {data_quality.get('kline_points', 0)} 根\n"
            report += f"- 技术指标: {data_quality.get('indicators_count', 0)} 个\n"
        
        # 添加时间戳
        report += f"\n⏰ 分析时间: {format_beijing_time(get_beijing_time().isoformat(), '%Y-%m-%d %H:%M:%S')}"
        
        return report
    
    async def _send_error_message(self, update: Update, error_msg: str):
        """发送错误消息"""
        try:
            if update.callback_query:
                await update.callback_query.edit_message_text(f"❌ {error_msg}")
            else:
                await update.message.reply_text(f"❌ {error_msg}")
        except Exception as e:
            logger.error(f"发送错误消息失败: {str(e)}")
    
    def dynamic_align_format(self, data_rows):
        """动态视图对齐格式化"""
        if not data_rows:
            return "暂无数据"
        
        # 计算每列的最大宽度
        max_widths = []
        for row in data_rows:
            for i, cell in enumerate(row):
                if i >= len(max_widths):
                    max_widths.append(0)
                max_widths[i] = max(max_widths[i], len(str(cell)))
        
        # 格式化每一行
        formatted_rows = []
        for row in data_rows:
            formatted_cells = []
            for i, cell in enumerate(row):
                cell_str = str(cell)
                if i == 0:  # 第一列左对齐
                    formatted_cells.append(cell_str.ljust(max_widths[i]))
                else:  # 其他列左对齐，用空格补足
                    formatted_cells.append(cell_str.ljust(max_widths[i]))
            
            formatted_line = ' '.join(formatted_cells)
            formatted_rows.append(formatted_line)
        
        return '\n'.join(formatted_rows)
    
    async def _handle_cancel_analysis(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """处理取消分析按钮 - 统一的返回主菜单逻辑"""
        try:
            query = update.callback_query
            await query.answer()
            
            # 清理用户状态
            context.user_data.clear()
            
            # 方案1：使用原代码的真正主菜单逻辑
            try:
                # 导入用户处理器和ensure_valid_text函数
                from crypto_trading_bot import user_handler, ensure_valid_text
                
                if user_handler:
                    text = user_handler.get_main_menu_text()
                    keyboard = user_handler.get_main_menu_keyboard()
                    
                    # 使用ensure_valid_text确保文本有效
                    text = ensure_valid_text(text, "⚡️ 钱大师 - 您的专属加密财富雷达")
                    
                    await query.edit_message_text(
                        text=text,
                        reply_markup=keyboard,
                        parse_mode='Markdown'
                    )
                    return ConversationHandler.END
                    
            except Exception as e:
                logger.error(f"方案1失败，尝试方案2: {str(e)}")
            
            # 方案2：如果user_handler为None，调用crypto_trading_bot的button_callback函数
            try:
                from crypto_trading_bot import button_callback
                
                # 创建模拟的callback_query来调用button_callback
                class MockCallbackQuery:
                    def __init__(self, original_query):
                        self.message = original_query.message
                        self.data = "main_menu"
                        self.from_user = original_query.from_user
                        self.id = original_query.id
                        self.chat_instance = original_query.chat_instance
                    
                    async def answer(self, text=None, show_alert=False, url=None, cache_time=0):
                        pass  # 已经调用过answer了
                    
                    async def edit_message_text(self, text, reply_markup=None, parse_mode=None, **kwargs):
                        await self.message.edit_text(text=text, reply_markup=reply_markup, parse_mode=parse_mode, **kwargs)
                
                # 创建模拟的update对象
                class MockUpdate:
                    def __init__(self, original_update):
                        self.callback_query = MockCallbackQuery(original_update.callback_query)
                        self.effective_user = original_update.effective_user
                        self.effective_chat = original_update.effective_chat
                        self.effective_message = original_update.effective_message
                
                mock_update = MockUpdate(update)
                await button_callback(mock_update, context)
                return ConversationHandler.END
                
            except Exception as e:
                logger.error(f"方案2失败，使用方案3: {str(e)}")
            
            # 方案3：最后备用方案，显示真正的主菜单内容（硬编码版本）
            try:
                from datetime import datetime
                current_time = format_beijing_time(get_beijing_time().isoformat(), "%H:%M")
                
                # 这是真正的主菜单文本内容
                main_menu_text = f"""⚡️ 钱大师 - 您的专属加密财富雷达

🛠 专业级加密货币市场分析引擎
🌐 全覆盖主流交易所，多维度数据深度挖掘
🤖 AI智能算法筛选潜力币种，先机尽在掌握
📡 实时订阅推送系统，市场脉搏24h监控
🎪 多策略适配框架，新手到专家全覆盖
🛋 告别盯盘焦虑，躺赢式轻松掌控行情
🌊 透视资金流向密码，精准把握市场风向
⚙️ 自动化策略生成器，AI赋能投资决策

🔥 【市场脉搏感知器】
├─ 📡 行情总览 → 全景扫描，一览无余
├─ 🎭 市场情绪 → 情绪温度计，恐贪指数
└─ 🎪 热币排行 → 涨跌风云榜，热度追踪

💰 【资金猎手系统】
├─ 🦅 主力动向 → 大鲸追踪，资金嗅探
├─ 🎯 套利机会 → 费率套利，稳健收益
└─ 🕷 庄家出货 → 爆仓预警，风险规避

🧬 【深度解析引擎】
├─ ⚖️ 超买超卖 → 买卖时机，成交量/市场综合排行榜
├─ 🌊 聪明钱流向 → 资金洪流，趋势把握
└─ 🔬 大户买卖盘 → 深度透视，订单解密

🤖 【AI智慧大脑】
├─ 🚨 异动警报 → 全天候AI警报
└─ 🎲 AI买卖点 → 算法加持，精准狙击

⚡ 【快速查询】
💡 输入 币种@ 快速查询（如：BTC@、ETH@）
🔤 支持大小写：btc@、BTC@、Btc@ 均可

🏛 【专属服务台】
├─ 💳 充值 → 能量补给站
├─ 👑 用户中心 → 个人指挥部
└─ 🆘 帮助 → 贴心助手

🆓 基础版：行情总览、市场情绪、热币排行
💎 高级版：套利机会、庄家出货、聪明钱流向、超买超卖、大户买卖盘（1积分/次）
💎 专业版：全功能解锁（50积分/日）
🚀 AI买卖点：智能分析加成（20积分/次）
📱 实时推送：异动信号即时到达
🗣 加入交易群参与讨论
👇 点击下方按钮开始获取财富密码

✉️ 交流群: t.me/zero2hero
💲 充值后联系客服进入交易群
👩‍💻 客服: @zancy1 @xiaocaixing @wangbw123

Powered by Zero 2 Hero Community（Z2H）
加密货币排行榜 - 实时数据监控
实时展示加密货币价格、持仓、资金流向、成交量、清算等多维度排行榜数据 (当前时间 {current_time})

"""
                
                # 这是真正的主菜单按钮布局
                main_menu_keyboard = [
                    [
                        InlineKeyboardButton("📡 行情总览", callback_data="basic_market"),
                        InlineKeyboardButton("🎭 市场情绪", callback_data="market_sentiment"),
                        InlineKeyboardButton("🎪 热币排行", callback_data="volume_ranking")
                    ],
                    [
                        InlineKeyboardButton("🦅 主力动向", callback_data="position_ranking"),
                        InlineKeyboardButton("🎯 套利机会", callback_data="funding_rate"),
                        InlineKeyboardButton("🕷️ 庄家出货", callback_data="liquidation_ranking")
                    ],
                    [
                        InlineKeyboardButton("⚖️ 超买超卖", callback_data="position_market_ratio"),
                        InlineKeyboardButton("🌊 聪明钱流向", callback_data="money_flow"),
                        InlineKeyboardButton("🔬 大户买卖盘", callback_data="market_depth")
                    ],
                    [
                        InlineKeyboardButton("🚨 异动警报", callback_data="aggregated_alerts"),
                        InlineKeyboardButton("🎲 AI买卖点", callback_data="coin_search")
                    ],
                    [
                        InlineKeyboardButton("💳 充值", callback_data="recharge"),
                        InlineKeyboardButton("👤 用户中心", callback_data="user_center"),
                        InlineKeyboardButton("ℹ️ 帮助", callback_data="help")
                    ],
                ]
                
                await query.edit_message_text(
                    text=main_menu_text,
                    reply_markup=InlineKeyboardMarkup(main_menu_keyboard),
                    parse_mode='Markdown'
                )
                return ConversationHandler.END
                
            except Exception as e:
                logger.error(f"方案3失败: {str(e)}")
                # 绝对最后的备用方案
                await query.edit_message_text(
                    "🏠 欢迎使用钱大师机器人\n\n"
                    "❌ 系统暂时繁忙，请稍后重试",
                    reply_markup=InlineKeyboardMarkup([[
                        InlineKeyboardButton("🔄 重试", callback_data="cancel_analysis")
                    ]])
                )
                return ConversationHandler.END
            
        except Exception as e:
            logger.error(f"处理取消分析失败: {str(e)}")
            return ConversationHandler.END
    

    
    async def handle_persistent_keyboard(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> int:
        """处理常驻键盘按钮"""
        try:
            message_text = update.message.text
            logger.info(f"ConversationHandler接收到常驻键盘按钮: {message_text}")
            
            # 清理AI对话状态
            if 'selected_symbol' in context.user_data:
                del context.user_data['selected_symbol']
            if 'selected_interval' in context.user_data:
                del context.user_data['selected_interval']

            if 'symbols_page' in context.user_data:
                del context.user_data['symbols_page']
            
            # 根据按钮类型处理
            if message_text == "🏠 主菜单":
                # 使用原代码的真正主菜单
                try:
                    # 导入用户处理器
                    from crypto_trading_bot import user_handler
                    if user_handler:
                        text = user_handler.get_main_menu_text()
                        keyboard = user_handler.get_main_menu_keyboard()
                        
                        # 直接发送主菜单
                        await update.message.reply_text(
                            text=text,
                            reply_markup=keyboard,
                            parse_mode='Markdown'
                        )
                        return ConversationHandler.END
                    else:
                        # 如果user_handler为None，显示简化主菜单
                        main_menu_text = """⚡️ 钱大师 - 您的专属加密财富雷达

🛠 专业级加密货币市场分析引擎
🌐 全覆盖主流交易所，多维度数据深度挖掘
🤖 AI智能算法筛选潜力币种，先机尽在掌握
📡 实时订阅推送系统，市场脉搏24h监控
🎪 多策略适配框架，新手到专家全覆盖
🛋 告别盯盘焦虑，躺赢式轻松掌控行情
🌊 透视资金流向密码，精准把握市场风向
⚙️ 自动化策略生成器，AI赋能投资决策

🔥 【市场脉搏感知器】
├─ 📡 行情总览 → 全景扫描，一览无余
├─ 🎭 市场情绪 → 情绪温度计，恐贪指数
└─ 🎪 热币排行 → 涨跌风云榜，热度追踪

💰 【资金猎手系统】
├─ 🦅 主力动向 → 大鲸追踪，资金嗅探
├─ 🎯 套利机会 → 费率套利，稳健收益
└─ 🕷 庄家出货 → 爆仓预警，风险规避

🧬 【深度解析引擎】
├─ ⚖️ 超买超卖 → 买卖时机，成交量/市场综合排行榜
├─ 🌊 聪明钱流向 → 资金洪流，趋势把握
└─ 🔬 大户买卖盘 → 深度透视，订单解密

🤖 【AI智慧大脑】
├─ 🚨 异动警报 → 全天候AI警报
└─ 🎲 AI买卖点 → 算法加持，精准狙击

⚡ 【快速查询】
💡 输入 币种@ 快速查询（如：BTC@、ETH@）
🔤 支持大小写：btc@、BTC@、Btc@ 均可

🏛 【专属服务台】
├─ 💳 充值 → 能量补给站
├─ 👑 用户中心 → 个人指挥部
└─ 🆘 帮助 → 贴心助手

🆓 基础版：行情总览、市场情绪、热币排行
💎 高级版：套利机会、庄家出货、聪明钱流向、超买超卖、大户买卖盘（1积分/次）
💎 专业版：全功能解锁（50积分/日）
🚀 AI买卖点：智能分析加成（20积分/次）
📱 实时推送：异动信号即时到达
🗣 加入交易群参与讨论
👇 点击下方按钮开始获取财富密码

✉️ 交流群: t.me/zero2hero
💲 充值后联系客服进入交易群
👩‍💻 客服: @zancy1 @xiaocaixing @wangbw123

Powered by Zero 2 Hero Community（Z2H）
加密货币排行榜 - 实时数据监控
实时展示加密货币价格、持仓、资金流向、成交量、清算等多维度排行榜数据 (当前时间 {time_info['hour_min']})

"""
                        
                        main_menu_keyboard = [
                            [
                                InlineKeyboardButton("📡 行情总览", callback_data="basic_market"),
                                InlineKeyboardButton("🎭 市场情绪", callback_data="market_sentiment"),
                                InlineKeyboardButton("🎪 热币排行", callback_data="volume_ranking")
                            ],
                            [
                                InlineKeyboardButton("🦅 主力动向", callback_data="position_ranking"),
                                InlineKeyboardButton("🎯 套利机会", callback_data="funding_rate"),
                                InlineKeyboardButton("🕷️ 庄家出货", callback_data="liquidation_ranking")
                            ],
                            [
                                InlineKeyboardButton("⚖️ 超买超卖", callback_data="position_market_ratio"),
                                InlineKeyboardButton("🌊 聪明钱流向", callback_data="money_flow"),
                                InlineKeyboardButton("🔬 大户买卖盘", callback_data="market_depth")
                            ],
                            [
                                InlineKeyboardButton("🚨 异动警报", callback_data="aggregated_alerts"),
                                InlineKeyboardButton("🎲 AI买卖点", callback_data="coin_search")
                            ],
                            [
                                InlineKeyboardButton("💳 充值", callback_data="recharge"),
                                InlineKeyboardButton("👤 用户中心", callback_data="user_center"),
                                InlineKeyboardButton("ℹ️ 帮助", callback_data="help")
                            ],
                        ]
                        
                        await update.message.reply_text(
                            main_menu_text,
                            reply_markup=InlineKeyboardMarkup(main_menu_keyboard),
                            parse_mode='Markdown'
                        )
                        return ConversationHandler.END
                        
                except Exception as e:
                    logger.error(f"显示主菜单失败: {str(e)}")
                    # 最后备用方案：使用main_menu回调按钮
                    await update.message.reply_text(
                        "🏠 返回主菜单",
                        reply_markup=InlineKeyboardMarkup([[
                            InlineKeyboardButton("🏠 主菜单", callback_data="main_menu")
                        ]])
                    )
                return ConversationHandler.END
            
            elif message_text == "🤖 AI点位":
                # 重新开始AI点位分析
                return await self.start_coin_analysis(update, context)
            
            elif message_text in ["👤 用户中心", "💳 充值", "🚨 异动警报 ", "ℹ️ 帮助"]:
                # 其他功能按钮，直接调用相应功能
                try:
                    # 导入相关函数
                    from crypto_trading_bot import show_user_center, show_recharge_menu, show_subscription_settings, help_command
                    
                    if message_text == "👤 用户中心":
                        await show_user_center(update, context)
                    elif message_text == "💳 充值":
                        await show_recharge_menu(update, context)
                    elif message_text == "🚨 异动警报 ":
                        await show_subscription_settings(update, context)
                    elif message_text == "ℹ️ 帮助":
                        await help_command(update, context)
                        
                except Exception as e:
                    logger.error(f"调用{message_text}功能失败: {str(e)}")
                    # 备用方案：发送回调按钮
                    button_actions = {
                        "👤 用户中心": "user_center",
                        "💳 充值": "recharge", 
                        "🚨 异动警报 ": "aggregated_alerts",
                        "ℹ️ 帮助": "help"
                    }
                    
                    action = button_actions.get(message_text, "cancel_analysis")
                    await update.message.reply_text(
                        f"{message_text}",
                        reply_markup=InlineKeyboardMarkup([[
                            InlineKeyboardButton(message_text, callback_data=action)
                        ]])
                    )
                return ConversationHandler.END
            
            else:
                # 未知按钮，默认返回主菜单
                try:
                    # 导入用户处理器
                    from crypto_trading_bot import user_handler
                    if user_handler:
                        text = user_handler.get_main_menu_text()
                        keyboard = user_handler.get_main_menu_keyboard()
                        
                        await update.message.reply_text(
                            text=text,
                            reply_markup=keyboard,
                            parse_mode='Markdown'
                        )
                        return ConversationHandler.END
                    else:
                        # 如果user_handler为None，显示简化主菜单
                        main_menu_text = """⚡️ 钱大师 - 您的专属加密财富雷达

🛠 专业级加密货币市场分析引擎
🌐 全覆盖主流交易所，多维度数据深度挖掘
🤖 AI智能算法筛选潜力币种，先机尽在掌握
📡 实时订阅推送系统，市场脉搏24h监控
🎪 多策略适配框架，新手到专家全覆盖
🛋 告别盯盘焦虑，躺赢式轻松掌控行情
🌊 透视资金流向密码，精准把握市场风向
⚙️ 自动化策略生成器，AI赋能投资决策

🔥 【市场脉搏感知器】
├─ 📡 行情总览 → 全景扫描，一览无余
├─ 🎭 市场情绪 → 情绪温度计，恐贪指数
└─ 🎪 热币排行 → 涨跌风云榜，热度追踪

💰 【资金猎手系统】
├─ 🦅 主力动向 → 大鲸追踪，资金嗅探
├─ 🎯 套利机会 → 费率套利，稳健收益
└─ 🕷 庄家出货 → 爆仓预警，风险规避

🧬 【深度解析引擎】
├─ ⚖️ 超买超卖 → 买卖时机，成交量/市场综合排行榜
├─ 🌊 聪明钱流向 → 资金洪流，趋势把握
└─ 🔬 大户买卖盘 → 深度透视，订单解密

🤖 【AI智慧大脑】
├─ 🚨 异动警报 → 全天候AI警报
└─ 🎲 AI买卖点 → 算法加持，精准狙击

⚡ 【快速查询】
💡 输入 币种@ 快速查询（如：BTC@、ETH@）
🔤 支持大小写：btc@、BTC@、Btc@ 均可

🏛 【专属服务台】
├─ 💳 充值 → 能量补给站
├─ 👑 用户中心 → 个人指挥部
└─ 🆘 帮助 → 贴心助手

🆓 基础版：行情总览、市场情绪、热币排行
💎 高级版：套利机会、庄家出货、聪明钱流向、超买超卖、大户买卖盘（1积分/次）
💎 专业版：全功能解锁（50积分/日）
🚀 AI买卖点：智能分析加成（20积分/次）
📱 实时推送：异动信号即时到达
🗣 加入交易群参与讨论
👇 点击下方按钮开始获取财富密码

✉️ 交流群: t.me/zero2hero
💲 充值后联系客服进入交易群
👩‍💻 客服: @zancy1 @xiaocaixing @wangbw123

Powered by Zero 2 Hero Community（Z2H）
加密货币排行榜 - 实时数据监控"""
                        
                        main_menu_keyboard = [
                            [
                                InlineKeyboardButton("📡 行情总览", callback_data="basic_market"),
                                InlineKeyboardButton("🎭 市场情绪", callback_data="market_sentiment"),
                                InlineKeyboardButton("🎪 热币排行", callback_data="volume_ranking")
                            ],
                            [
                                InlineKeyboardButton("🦅 主力动向", callback_data="position_ranking"),
                                InlineKeyboardButton("🎯 套利机会", callback_data="funding_rate"),
                                InlineKeyboardButton("🕷️ 庄家出货", callback_data="liquidation_ranking")
                            ],
                            [
                                InlineKeyboardButton("⚖️ 超买超卖", callback_data="position_market_ratio"),
                                InlineKeyboardButton("🌊 聪明钱流向", callback_data="money_flow"),
                                InlineKeyboardButton("🔬 大户买卖盘", callback_data="market_depth")
                            ],
                            [
                                InlineKeyboardButton("🚨 异动警报", callback_data="aggregated_alerts"),
                                InlineKeyboardButton("🎲 AI买卖点", callback_data="coin_search")
                            ],
                            [
                                InlineKeyboardButton("💳 充值", callback_data="recharge"),
                                InlineKeyboardButton("👤 用户中心", callback_data="user_center"),
                                InlineKeyboardButton("ℹ️ 帮助", callback_data="help")
                            ],
                        ]
                        
                        await update.message.reply_text(
                            main_menu_text,
                            reply_markup=InlineKeyboardMarkup(main_menu_keyboard),
                            parse_mode='Markdown'
                        )
                        return ConversationHandler.END
                        
                except Exception as e:
                    logger.error(f"显示主菜单失败: {str(e)}")
                    # 最后备用方案：使用main_menu回调按钮
                    await update.message.reply_text(
                        "🏠 返回主菜单",
                        reply_markup=InlineKeyboardMarkup([[
                            InlineKeyboardButton("🏠 主菜单", callback_data="main_menu")
                        ]])
                    )
                return ConversationHandler.END
                
        except Exception as e:
            logger.error(f"处理常驻键盘按钮失败: {str(e)}")
            return ConversationHandler.END
    
    def get_conversation_handler(self) -> ConversationHandler:
        """获取对话处理器"""
        from telegram.ext import CallbackQueryHandler, MessageHandler, filters
        
        return ConversationHandler(
            entry_points=[
                # 启动币种分析
                CallbackQueryHandler(self.start_coin_analysis, pattern="^start_coin_analysis$"),
                # 直接处理具体币种选择（coin_BTCUSDT, coin_ETHUSDT, coin_1000SHIBUSDT等）
                CallbackQueryHandler(self.handle_coin_selection, pattern="^coin_[A-Z0-9]{2,15}USDT$"),
                # 直接处理时间周期选择（interval_1h, interval_4h等）
                CallbackQueryHandler(self.handle_interval_selection, pattern="^interval_[0-9]+[mhd]$"),

            ],
            states={
                SELECTING_COIN: [
                    CallbackQueryHandler(self.handle_coin_selection),
                    # 添加常驻键盘按钮处理
                    MessageHandler(filters.Regex("^(🏠 主菜单|🎲 AI买卖点|👤 用户中心|💳 充值|🚨 异动警报|ℹ️ 帮助)$"), self.handle_persistent_keyboard)
                ],
                SELECTING_INTERVAL: [
                    CallbackQueryHandler(self.handle_interval_selection),
                    # 添加常驻键盘按钮处理 - 必须放在通用文本处理前面
                    MessageHandler(filters.Regex("^(🏠 主菜单|🎲 AI买卖点|👤 用户中心|💳 充值|🚨 异动警报|ℹ️ 帮助)$"), self.handle_persistent_keyboard)
                ]
            },
            fallbacks=[
                # 取消分析 - 显示主菜单并结束对话
                CallbackQueryHandler(self._handle_cancel_analysis, pattern="^cancel_analysis$"),
                # 返回主菜单 - 统一使用cancel_analysis处理逻辑
                CallbackQueryHandler(self._handle_cancel_analysis, pattern="^main_menu$"),
                # 返回币种选择
                CallbackQueryHandler(self.start_coin_analysis, pattern="^back_to_coin$"),
                # 返回币种选择（另一个模式）
                CallbackQueryHandler(self.start_coin_analysis, pattern="^back_to_coin_selection$"),
                # 处理其他可能的退出情况 - 扩展所有功能按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^(position_ranking|funding_rate|volume_ranking|basic_market|market_sentiment|liquidation_ranking|position_market_ratio|money_flow|market_depth|user_center|recharge|aggregated_alerts|help|admin_panel)$"),
                # 处理资金流向相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^money_flow_(limit|period|sort|type|market)_.*$"),
                # 处理基础行情相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^basic_market_(limit|period|sort|market)_.*$"),
                # 处理持仓量相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^position_(limit|period|sort|market)_.*$"),
                # 处理资金费率相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^funding_(limit|period|sort|market)_.*$"),
                # 处理交易量相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^volume_(limit|period|sort|market)_.*$"),
                # 处理爆仓相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^liquidation_(limit|period|sort|market)_.*$"),
                # 处理持仓市值比相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^position_market_ratio_(limit|period|sort|market)_.*$"),
                # 处理市场深度相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^market_depth_(limit|period|sort|market)_.*$"),
                # 处理市场情绪相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^market_sentiment_(limit|period|sort|market)_.*$"),
                # 处理用户中心相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^(user_info|balance|points|invite|withdrawal|transaction_history)$"),
                # 处理充值相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^(recharge_.*|payment_.*|confirm_payment)$"),
                # 处理订阅相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^(subscribe_.*|unsubscribe_.*|alert_settings)$"),
                # 处理管理员相关按钮
                CallbackQueryHandler(lambda u, c: ConversationHandler.END, pattern="^admin_.*$"),
                # 处理分页按钮 - 使用专门的分页处理器
                CallbackQueryHandler(self.handle_pagination_callback, pattern="^page_.*$"),
                CallbackQueryHandler(self.handle_pagination_callback, pattern="^reanalyze_.*$"),
                CallbackQueryHandler(lambda u, c: None, pattern="^page_info$"),  # 页面信息按钮无操作
                # 添加常驻键盘按钮的fallback处理
                MessageHandler(filters.Regex("^(🏠 主菜单|🤖 AI点位|�� 用户中心|💳 充值|🚨 异动警报 |ℹ️ 帮助)$"), self.handle_persistent_keyboard),
                # 添加通用的CallbackQuery处理，确保任何未匹配的按钮都能结束对话
                CallbackQueryHandler(lambda u, c: ConversationHandler.END)
            ]
        )


# 测试函数
async def test_telegram_handler():
    """测试Telegram处理器"""
    from AI_coin_query_manager import AICoinQueryManager
    
    # 创建处理器
    query_manager = AICoinQueryManager()
    telegram_handler = AITelegramHandler(query_manager)
    
    print("✅ Telegram处理器创建成功")
    print(f"支持的币种数量: {len(telegram_handler.query_manager.get_supported_symbols())}")
    print(f"支持的时间周期: {telegram_handler.query_manager.get_available_intervals()}")


if __name__ == "__main__":
    asyncio.run(test_telegram_handler()) 