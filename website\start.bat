@echo off
chcp 65001 >nul

echo 🚀 启动加密货币行情网站...

REM 检查Python是否安装
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python 未安装，请先安装Python
    pause
    exit /b 1
)

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装Node.js
    pause
    exit /b 1
)

REM 检查npm是否安装
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装，请先安装npm
    pause
    exit /b 1
)

echo ✅ 环境检查通过

REM 安装后端依赖
echo 📦 安装后端依赖...
cd backend

if not exist "venv" (
    echo 创建Python虚拟环境...
    python -m venv venv
)

call venv\Scripts\activate.bat
pip install -r requirements.txt

echo ✅ 后端依赖安装完成

REM 启动后端服务
echo 🔧 启动后端服务...
start "Backend Server" cmd /k "call venv\Scripts\activate.bat && python main.py"

echo ✅ 后端服务已启动

REM 等待后端服务启动
timeout /t 3 /nobreak >nul

REM 安装前端依赖
echo 📦 安装前端依赖...
cd ..\frontend

if not exist "node_modules" (
    echo 安装前端依赖包...
    npm install
)

echo ✅ 前端依赖安装完成

REM 启动前端服务
echo 🔧 启动前端服务...
start "Frontend Server" cmd /k "npm start"

echo ✅ 前端服务已启动

echo.
echo 🎉 网站启动成功！
echo 📊 后端API: http://localhost:8000
echo 🌐 前端网站: http://localhost:3000
echo.
echo 按任意键退出...
pause >nul