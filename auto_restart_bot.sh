#!/bin/bash

# 自动重启脚本 - 每4小时重启一次crypto_trading_bot.py
# 作者: <PERSON>ro AI Assistant
# 使用方法: chmod +x auto_restart_bot.sh && ./auto_restart_bot.sh

# 配置变量
PROJECT_DIR="/home/<USER>/Project-bot"
PYTHON_PATH="/home/<USER>/Project-bot/venv/bin/python"
SCRIPT_NAME="crypto_trading_bot.py"
LOG_FILE="bot.log"
PID_FILE="bot.pid"
RESTART_INTERVAL=14400  # 4小时 = 4 * 60 * 60 = 14400秒

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a restart.log
}

# 检查进程是否运行
is_running() {
    if [ -f "$PID_FILE" ]; then
        local pid=$(cat "$PID_FILE")
        if ps -p "$pid" > /dev/null 2>&1; then
            return 0
        else
            rm -f "$PID_FILE"
            return 1
        fi
    fi
    return 1
}

# 启动机器人
start_bot() {
    cd "$PROJECT_DIR"
    
    if is_running; then
        log "机器人已经在运行中 (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    log "启动机器人..."
    nohup "$PYTHON_PATH" -u "$SCRIPT_NAME" > "$LOG_FILE" 2>&1 &
    local pid=$!
    echo $pid > "$PID_FILE"
    
    # 等待2秒检查是否启动成功
    sleep 2
    if is_running; then
        log "机器人启动成功 (PID: $pid)"
        echo -e "${GREEN}✓ 机器人启动成功${NC}"
        return 0
    else
        log "机器人启动失败"
        echo -e "${RED}✗ 机器人启动失败${NC}"
        return 1
    fi
}

# 停止机器人
stop_bot() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        log "停止机器人 (PID: $pid)..."
        kill "$pid"
        
        # 等待进程结束
        local count=0
        while ps -p "$pid" > /dev/null 2>&1 && [ $count -lt 10 ]; do
            sleep 1
            count=$((count + 1))
        done
        
        # 如果进程仍在运行，强制杀死
        if ps -p "$pid" > /dev/null 2>&1; then
            log "强制停止机器人..."
            kill -9 "$pid"
        fi
        
        rm -f "$PID_FILE"
        log "机器人已停止"
        echo -e "${YELLOW}机器人已停止${NC}"
        return 0
    else
        log "机器人未运行"
        echo -e "${YELLOW}机器人未运行${NC}"
        return 1
    fi
}

# 重启机器人
restart_bot() {
    log "重启机器人..."
    echo -e "${YELLOW}正在重启机器人...${NC}"
    stop_bot
    sleep 2
    start_bot
}

# 主循环 - 自动重启模式
auto_restart_loop() {
    log "启动自动重启模式 (每4小时重启一次)"
    echo -e "${GREEN}自动重启模式已启动 - 每4小时重启一次${NC}"
    echo -e "${YELLOW}按 Ctrl+C 停止自动重启${NC}"
    
    # 首次启动
    start_bot
    
    # 主循环
    while true; do
        log "等待 $RESTART_INTERVAL 秒后重启..."
        sleep $RESTART_INTERVAL
        restart_bot
    done
}

# 信号处理 - 优雅退出
cleanup() {
    log "收到退出信号，正在清理..."
    echo -e "\n${YELLOW}正在停止自动重启脚本...${NC}"
    stop_bot
    log "自动重启脚本已停止"
    exit 0
}

# 设置信号处理
trap cleanup SIGINT SIGTERM

# 显示使用帮助
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  start     启动机器人"
    echo "  stop      停止机器人"
    echo "  restart   重启机器人"
    echo "  status    查看机器人状态"
    echo "  auto      启动自动重启模式 (默认)"
    echo "  help      显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 auto     # 启动自动重启模式"
    echo "  $0 start    # 仅启动一次"
    echo "  $0 status   # 查看状态"
}

# 查看状态
show_status() {
    if is_running; then
        local pid=$(cat "$PID_FILE")
        echo -e "${GREEN}✓ 机器人正在运行${NC} (PID: $pid)"
        
        # 显示运行时间
        local start_time=$(ps -o lstart= -p "$pid" 2>/dev/null)
        if [ -n "$start_time" ]; then
            echo "启动时间: $start_time"
        fi
        
        # 显示最近的日志
        if [ -f "$LOG_FILE" ]; then
            echo ""
            echo "最近的日志:"
            tail -5 "$LOG_FILE"
        fi
    else
        echo -e "${RED}✗ 机器人未运行${NC}"
    fi
}

# 主程序
main() {
    # 切换到项目目录
    cd "$PROJECT_DIR" || {
        echo -e "${RED}错误: 无法进入项目目录 $PROJECT_DIR${NC}"
        exit 1
    }
    
    # 检查Python路径
    if [ ! -f "$PYTHON_PATH" ]; then
        echo -e "${RED}错误: Python解释器不存在 $PYTHON_PATH${NC}"
        exit 1
    fi
    
    # 检查脚本文件
    if [ ! -f "$SCRIPT_NAME" ]; then
        echo -e "${RED}错误: 脚本文件不存在 $SCRIPT_NAME${NC}"
        exit 1
    fi
    
    # 处理命令行参数
    case "${1:-auto}" in
        "start")
            start_bot
            ;;
        "stop")
            stop_bot
            ;;
        "restart")
            restart_bot
            ;;
        "status")
            show_status
            ;;
        "auto")
            auto_restart_loop
            ;;
        "help"|"-h"|"--help")
            show_help
            ;;
        *)
            echo -e "${RED}未知选项: $1${NC}"
            show_help
            exit 1
            ;;
    esac
}

# 运行主程序
main "$@"