#!/bin/bash

# 设置systemd服务脚本
# 这个脚本会创建一个systemd服务来管理你的机器人

SERVICE_NAME="crypto-trading-bot"
PROJECT_DIR="/home/<USER>/Project-bot"
USER="ubuntu"

# 创建systemd服务文件
create_service_file() {
    sudo tee /etc/systemd/system/${SERVICE_NAME}.service > /dev/null <<EOF
[Unit]
Description=Crypto Trading Bot with Auto Restart
After=network.target
Wants=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=$PROJECT_DIR
ExecStart=$PROJECT_DIR/venv/bin/python -u crypto_trading_bot.py
Restart=always
RestartSec=10
StandardOutput=append:$PROJECT_DIR/bot.log
StandardError=append:$PROJECT_DIR/bot.log

# 环境变量
Environment=PYTHONUNBUFFERED=1

# 安全设置
NoNewPrivileges=true
PrivateTmp=true

[Install]
WantedBy=multi-user.target
EOF
}

# 创建定时重启的timer
create_timer_file() {
    sudo tee /etc/systemd/system/${SERVICE_NAME}-restart.timer > /dev/null <<EOF
[Unit]
Description=Restart Crypto Trading Bot every 4 hours
Requires=${SERVICE_NAME}.service

[Timer]
OnBootSec=4h
OnUnitActiveSec=4h
Persistent=true

[Install]
WantedBy=timers.target
EOF
}

# 创建重启服务
create_restart_service() {
    sudo tee /etc/systemd/system/${SERVICE_NAME}-restart.service > /dev/null <<EOF
[Unit]
Description=Restart Crypto Trading Bot
Requires=${SERVICE_NAME}.service

[Service]
Type=oneshot
ExecStart=/bin/systemctl restart ${SERVICE_NAME}.service
EOF
}

echo "正在设置 $SERVICE_NAME 服务..."

# 创建服务文件
create_service_file
create_timer_file
create_restart_service

# 重新加载systemd
sudo systemctl daemon-reload

# 启用服务
sudo systemctl enable ${SERVICE_NAME}.service
sudo systemctl enable ${SERVICE_NAME}-restart.timer

# 启动服务和定时器
echo "正在启动服务..."
sudo systemctl start ${SERVICE_NAME}.service
sudo systemctl start ${SERVICE_NAME}-restart.timer

echo ""
echo "✓ 服务设置完成并已启动！"
echo "✓ 开机自启动已启用！"
echo "✓ 每4小时自动重启已启用！"
echo ""
echo "常用命令:"
echo "查看服务状态: sudo systemctl status $SERVICE_NAME"
echo "查看实时日志: sudo journalctl -u $SERVICE_NAME -f"
echo "查看定时器: sudo systemctl status ${SERVICE_NAME}-restart.timer"
echo "停止服务: sudo systemctl stop $SERVICE_NAME"
echo "重启服务: sudo systemctl restart $SERVICE_NAME"
echo ""
echo "取消开机自启: sudo systemctl disable $SERVICE_NAME"
echo "取消定时重启: sudo systemctl disable ${SERVICE_NAME}-restart.timer"