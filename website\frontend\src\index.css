@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    font-feature-settings: "rlig" 1, "calt" 1;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 h-10 py-2 px-4;
  }

  .btn-secondary {
    @apply btn bg-gray-100 text-gray-900 hover:bg-gray-200 h-10 py-2 px-4;
  }

  .btn-ghost {
    @apply btn hover:bg-gray-100 hover:text-gray-900 h-10 py-2 px-4;
  }

  .card {
    @apply rounded-lg border bg-white text-gray-950 shadow-sm;
  }

  .input {
    @apply flex h-10 w-full rounded-md border border-gray-200 bg-white px-3 py-2 text-sm ring-offset-white file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary-500 focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50;
  }

  .table {
    @apply w-full caption-bottom text-sm;
  }

  .table-header {
    @apply border-b;
  }

  .table-row {
    @apply border-b transition-colors hover:bg-gray-50/50 data-[state=selected]:bg-gray-50;
  }

  .table-head {
    @apply h-12 px-4 text-left align-middle font-medium text-gray-500 [&:has([role=checkbox])]:pr-0;
  }

  .table-cell {
    @apply p-4 align-middle [&:has([role=checkbox])]:pr-0;
  }

  .price-up {
    @apply text-green-600;
  }

  .price-down {
    @apply text-red-600;
  }

  .price-neutral {
    @apply text-gray-600;
  }

  .loading-spinner {
    @apply animate-spin rounded-full border-2 border-gray-300 border-t-primary-600;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
}