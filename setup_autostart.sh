#!/bin/bash

# 开机自启动设置脚本
# 使用crontab实现开机自启和定时重启

PROJECT_DIR="/home/<USER>/Project-bot"
SCRIPT_PATH="$PROJECT_DIR/auto_restart_bot.sh"

echo "正在设置开机自启动..."

# 确保auto_restart_bot.sh有执行权限
chmod +x "$SCRIPT_PATH"

# 备份当前的crontab
crontab -l > /tmp/crontab_backup_$(date +%Y%m%d_%H%M%S) 2>/dev/null || true

# 创建新的crontab条目
{
    # 保留现有的crontab条目
    crontab -l 2>/dev/null || true
    
    # 添加开机自启动（每次重启后1分钟启动）
    echo "@reboot sleep 60 && cd $PROJECT_DIR && ./auto_restart_bot.sh auto >> startup.log 2>&1"
    
    # 添加每4小时重启一次的任务（作为备用，如果auto_restart_bot.sh失效）
    echo "0 */4 * * * cd $PROJECT_DIR && ./auto_restart_bot.sh restart >> cron_restart.log 2>&1"
    
} | crontab -

echo "✓ 开机自启动设置完成！"
echo ""
echo "设置内容："
echo "1. 开机后1分钟自动启动机器人（自动重启模式）"
echo "2. 每4小时执行一次重启（双重保险）"
echo ""
echo "查看当前crontab: crontab -l"
echo "查看启动日志: tail -f $PROJECT_DIR/startup.log"
echo "查看重启日志: tail -f $PROJECT_DIR/cron_restart.log"
echo ""
echo "手动测试启动: cd $PROJECT_DIR && ./auto_restart_bot.sh auto"
echo ""
echo "如需取消开机自启，请编辑: crontab -e"