import axios, { AxiosResponse } from 'axios';
import {
  Cryptocurrency,
  CryptocurrencyDetail,
  MarketOverview,
  ApiResponse,
  PaginatedResponse,
  ApiError
} from '../types';

// API基础配置
const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

const api = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
api.interceptors.request.use(
  (config) => {
    console.log(`API请求: ${config.method?.toUpperCase()} ${config.url}`);
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    const apiError: ApiError = {
      message: error.response?.data?.detail || error.message || '网络错误',
      status: error.response?.status || 0,
      details: error.response?.data
    };

    console.error('API错误:', apiError);
    return Promise.reject(apiError);
  }
);

// API服务类
export class ApiService {
  // 获取市场概览
  static async getMarketOverview(): Promise<MarketOverview> {
    const response = await api.get<MarketOverview>('/api/market/overview');
    return response.data;
  }

  // 获取加密货币列表
  static async getCryptocurrencies(params: {
    page?: number;
    limit?: number;
    sort_by?: string;
    order?: string;
    search?: string;
  } = {}): Promise<PaginatedResponse<Cryptocurrency[]>> {
    const response = await api.get<PaginatedResponse<Cryptocurrency[]>>('/api/cryptocurrencies', {
      params
    });
    return response.data;
  }

  // 获取特定加密货币详情
  static async getCryptocurrencyDetail(symbol: string): Promise<CryptocurrencyDetail> {
    const response = await api.get<CryptocurrencyDetail>(`/api/cryptocurrencies/${symbol}`);
    return response.data;
  }

  // 获取涨幅榜
  static async getTopGainers(limit: number = 10): Promise<ApiResponse<Cryptocurrency[]>> {
    const response = await api.get<ApiResponse<Cryptocurrency[]>>('/api/market/gainers', {
      params: { limit }
    });
    return response.data;
  }

  // 获取跌幅榜
  static async getTopLosers(limit: number = 10): Promise<ApiResponse<Cryptocurrency[]>> {
    const response = await api.get<ApiResponse<Cryptocurrency[]>>('/api/market/losers', {
      params: { limit }
    });
    return response.data;
  }
}

// WebSocket服务类
export class WebSocketService {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectInterval = 3000;
  private listeners: { [key: string]: Function[] } = {};

  constructor(private url: string = `ws://localhost:8000/ws/market`) {}

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.ws = new WebSocket(this.url);

        this.ws.onopen = () => {
          console.log('WebSocket连接已建立');
          this.reconnectAttempts = 0;
          this.emit('connection', { status: 'connected' });
          resolve();
        };

        this.ws.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            this.emit('market_data', data);
          } catch (error) {
            console.error('解析WebSocket消息失败:', error);
          }
        };

        this.ws.onclose = () => {
          console.log('WebSocket连接已关闭');
          this.emit('connection', { status: 'disconnected' });
          this.handleReconnect();
        };

        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error);
          this.emit('error', { error });
          reject(error);
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  disconnect(): void {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  private handleReconnect(): void {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重连 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);

      setTimeout(() => {
        this.connect().catch(console.error);
      }, this.reconnectInterval);
    } else {
      console.error('WebSocket重连失败，已达到最大重试次数');
      this.emit('error', { error: '连接失败' });
    }
  }

  on(event: string, callback: Function): void {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    this.listeners[event].push(callback);
  }

  off(event: string, callback: Function): void {
    if (this.listeners[event]) {
      this.listeners[event] = this.listeners[event].filter(cb => cb !== callback);
    }
  }

  private emit(event: string, data: any): void {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data));
    }
  }
}

export default ApiService;