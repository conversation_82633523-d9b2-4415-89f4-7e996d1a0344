#!/bin/bash

# 加密货币行情网站启动脚本

echo "🚀 启动加密货币行情网站..."

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装Python3"
    exit 1
fi

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装npm"
    exit 1
fi

echo "✅ 环境检查通过"

# 安装后端依赖
echo "📦 安装后端依赖..."
cd backend
if [ ! -d "venv" ]; then
    echo "创建Python虚拟环境..."
    python3 -m venv venv
fi

source venv/bin/activate
pip install -r requirements.txt

echo "✅ 后端依赖安装完成"

# 启动后端服务
echo "🔧 启动后端服务..."
python main.py &
BACKEND_PID=$!

echo "✅ 后端服务已启动 (PID: $BACKEND_PID)"

# 等待后端服务启动
sleep 3

# 安装前端依赖
echo "📦 安装前端依赖..."
cd ../frontend

if [ ! -d "node_modules" ]; then
    echo "安装前端依赖包..."
    npm install
fi

echo "✅ 前端依赖安装完成"

# 启动前端服务
echo "🔧 启动前端服务..."
npm start &
FRONTEND_PID=$!

echo "✅ 前端服务已启动 (PID: $FRONTEND_PID)"

echo ""
echo "🎉 网站启动成功！"
echo "📊 后端API: http://localhost:8000"
echo "🌐 前端网站: http://localhost:3000"
echo ""
echo "按 Ctrl+C 停止服务"

# 等待用户中断
trap "echo ''; echo '🛑 正在停止服务...'; kill $BACKEND_PID $FRONTEND_PID; exit" INT

# 保持脚本运行
wait