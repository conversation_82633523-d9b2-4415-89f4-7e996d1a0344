#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
管理员数据分析报告生成器（简化版）
专用于subprocess调用，避免Unicode编码问题
"""

import os
import sys
import json
import locale
import logging
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from typing import Dict, List, Any, Optional

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 设置编码兼容性
if sys.platform.startswith('win'):
    try:
        locale.setlocale(locale.LC_ALL, 'Chinese_China.utf8')
    except:
        try:
            locale.setlocale(locale.LC_ALL, 'zh_CN.UTF-8')
        except:
            pass

# 确保stdout使用UTF-8编码
if hasattr(sys.stdout, 'reconfigure'):
    sys.stdout.reconfigure(encoding='utf-8')
elif hasattr(sys.stdout, 'buffer'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')

class AdminReportGenerator:
    """管理员数据报告生成器"""
    
    def __init__(self):
        """初始化生成器"""
        self.data_dir = "data"
        self.user_data_file = os.path.join(self.data_dir, "user_data.json")
        self.points_history_file = os.path.join(self.data_dir, "points_history.json")
        
        # 测试账号ID列表（需要在数据分析中排除的用户）
        self.test_user_ids = {
            6511182257,   # desci0
            7726231059,   # xiaocaixing
            7029331985,   # zancy1
            6556011444,   # wangbw123
            1350723225,   # Jeffxxxg
            5621123086,   # bot
            7777895335,   # ld666cnm
            7996222536,   # 123456789
            7314956191,   # 123456789
            7177584680,   # 123456789
            8065939278,   # 123456789
        }
        
        # 功能名称映射（英文 -> 中文）
        self.function_name_mapping = {
            'position_ranking': '持仓排行',
            'funding_rate': '资金费率',
            'volume_ranking': '交易量排行',
            'liquidation_ranking': '爆仓排行',
            'market_sentiment': '市场情绪',
            'basic_market': '基础行情',
            'position_market_ratio': '持仓市值比',
            'money_flow': '资金流向',
            'market_depth': '市场深度',
            'coin_search': 'AI点位分析',
            'aggregated_alerts': '信号订阅',
            'daily_signal_subscription': '每日信号订阅',
            # 添加更多功能映射
        }
    
    def load_data(self) -> tuple:
        """加载数据文件"""
        try:
            # 加载用户数据
            with open(self.user_data_file, 'r', encoding='utf-8') as f:
                user_data = json.load(f)
            
            # 加载积分历史
            with open(self.points_history_file, 'r', encoding='utf-8') as f:
                points_history = json.load(f)
            
            return user_data, points_history
        except Exception as e:
            print(f"数据加载失败: {e}")
            return {}, []
    
    def filter_admin_data(self, user_data: dict, points_history: list) -> tuple:
        """过滤掉管理员测试数据"""
        # 过滤用户数据
        filtered_users = {
            uid: data for uid, data in user_data.items() 
            if int(uid) not in self.test_user_ids
        }
        
        # 过滤积分历史
        filtered_points = [
            record for record in points_history 
            if record.get('user_id') not in self.test_user_ids
        ]
        
        return filtered_users, filtered_points
    
    def analyze_core_metrics(self, user_data: dict, points_history: list) -> dict:
        """分析核心业务指标"""
        total_users = len(user_data)
        active_subscribers = sum(1 for user in user_data.values() 
                                if user.get('subscription_active', False))
        
        # 积分统计
        total_points_pool = sum(user.get('points', 0) for user in user_data.values())
        total_recharge = sum(user.get('total_recharge', 0) for user in user_data.values())
        
        avg_points = total_points_pool / total_users if total_users > 0 else 0
        avg_recharge = total_recharge / total_users if total_users > 0 else 0
        
        subscription_rate = (active_subscribers / total_users * 100) if total_users > 0 else 0
        
        return {
            'total_users': total_users,
            'active_subscribers': active_subscribers,
            'subscription_rate': subscription_rate,
            'total_points_pool': total_points_pool,
            'total_recharge': total_recharge,
            'avg_points': avg_points,
            'avg_recharge': avg_recharge,
            'points_records_count': len(points_history)
        }
    
    def analyze_activity(self, user_data: dict) -> dict:
        """分析用户活跃度"""
        now = datetime.now()
        week_ago = now - timedelta(days=7)
        month_ago = now - timedelta(days=30)
        
        active_7d = 0
        active_30d = 0
        
        for user in user_data.values():
            last_active = user.get('last_active')
            if last_active:
                try:
                    last_active_dt = datetime.fromisoformat(last_active.replace('Z', '+00:00'))
                    if last_active_dt.replace(tzinfo=None) >= week_ago:
                        active_7d += 1
                    if last_active_dt.replace(tzinfo=None) >= month_ago:
                        active_30d += 1
                except:
                    pass
        
        total_users = len(user_data)
        return {
            'active_7d': active_7d,
            'active_30d': active_30d,
            'active_7d_rate': (active_7d / total_users * 100) if total_users > 0 else 0,
            'active_30d_rate': (active_30d / total_users * 100) if total_users > 0 else 0
        }
    
    def analyze_points_economy(self, points_history: list) -> dict:
        """分析积分经济"""
        income_records = []
        expense_records = []
        
        for record in points_history:
            change = record.get('change', 0)
            if change > 0:
                income_records.append(record)
            elif change < 0:
                expense_records.append(record)
        
        total_income = sum(r.get('change', 0) for r in income_records)
        total_expense = sum(abs(r.get('change', 0)) for r in expense_records)
        
        # 分类统计
        expense_categories = defaultdict(int)
        income_categories = defaultdict(int)
        
        for record in expense_records:
            reason = record.get('reason', '未知')
            expense_categories[reason] += abs(record.get('change', 0))
        
        for record in income_records:
            reason = record.get('reason', '未知')
            income_categories[reason] += record.get('change', 0)
        
        return {
            'total_income': total_income,
            'total_expense': total_expense,
            'net_flow': total_income - total_expense,
            'expense_categories': dict(expense_categories),
            'income_categories': dict(income_categories)
        }
    
    def get_user_rankings(self, user_data: dict) -> dict:
        """获取用户排行榜"""
        # 积分排行
        points_ranking = sorted(
            [(uid, data.get('points', 0), data.get('username', f'User_{uid}')) 
             for uid, data in user_data.items()],
            key=lambda x: x[1], reverse=True
        )[:10]
        
        # 充值排行
        recharge_ranking = sorted(
            [(uid, data.get('total_recharged', 0), data.get('username', f'User_{uid}')) 
             for uid, data in user_data.items()],
            key=lambda x: x[1], reverse=True
        )[:10]
        
        return {
            'points_ranking': points_ranking,
            'recharge_ranking': recharge_ranking
        }
    
    def analyze_daily_activity(self, points_history: list) -> dict:
        """分析近7天每日活动"""
        now = datetime.now()
        daily_stats = {}
        
        for i in range(7):
            date = (now - timedelta(days=i)).strftime('%Y-%m-%d')
            daily_stats[date] = {
                'transactions': 0,
                'active_users': set(),
                'points_earned': 0,
                'points_spent': 0
            }
        
        for record in points_history:
            timestamp = record.get('timestamp', '')
            if timestamp:
                try:
                    record_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).strftime('%Y-%m-%d')
                    if record_date in daily_stats:
                        daily_stats[record_date]['transactions'] += 1
                        daily_stats[record_date]['active_users'].add(record.get('user_id'))
                        
                        change = record.get('change', 0)
                        if change > 0:
                            daily_stats[record_date]['points_earned'] += change
                        else:
                            daily_stats[record_date]['points_spent'] += abs(change)
                except:
                    pass
        
        # 转换set为count
        for date_stats in daily_stats.values():
            date_stats['active_users'] = len(date_stats['active_users'])
        
        return daily_stats
    
    def analyze_function_usage(self, points_history: list) -> dict:
        """分析功能使用情况和积分消耗"""
        # 按功能统计累计消耗
        function_consumption = defaultdict(int)
        
        # 按日期和功能统计每日消耗
        daily_function_consumption = defaultdict(lambda: defaultdict(int))
        
        # 处理积分历史记录
        for record in points_history:
            reason = record.get('reason', '')
            change = record.get('change', 0)
            timestamp = record.get('timestamp', '')
            
            # 只处理消费记录（负数积分）
            if change >= 0:
                continue
            
            points_consumed = abs(change)
            
            # 提取功能名称
            function_name = self.extract_function_name(reason)
            if function_name:
                # 累计消耗统计
                function_consumption[function_name] += points_consumed
                
                # 每日消耗统计
                if timestamp:
                    try:
                        record_date = datetime.fromisoformat(timestamp.replace('Z', '+00:00')).strftime('%Y-%m-%d')
                        daily_function_consumption[record_date][function_name] += points_consumed
                    except:
                        pass
        
        return {
            'total_consumption': dict(function_consumption),
            'daily_consumption': dict(daily_function_consumption)
        }
    
    def extract_function_name(self, reason: str) -> str:
        """从原因字符串中提取功能名称"""
        reason = reason.lower()
        
        # 检查各种可能的功能标识
        if 'coin_search' in reason or 'ai' in reason:
            return '🤖 AI点位分析'
        elif 'daily_signal' in reason or '每日信号' in reason:
            return '🔔 每日信号订阅'
        elif 'position_ranking' in reason or '持仓' in reason:
            return '📊 持仓排行'
        elif 'funding_rate' in reason or '资金费率' in reason:
            return '💲 资金费率'
        elif 'volume_ranking' in reason or '交易量' in reason:
            return '💹 交易量排行'
        elif 'liquidation' in reason or '爆仓' in reason:
            return '💥 爆仓排行'
        elif 'market_sentiment' in reason or '市场情绪' in reason:
            return '📈 市场情绪'
        elif 'basic_market' in reason or '基础行情' in reason:
            return '💹 基础行情'
        elif 'position_market_ratio' in reason or '市值比' in reason:
            return '📊 持仓市值比'
        elif 'money_flow' in reason or '资金流向' in reason:
            return '💰 资金流向'
        elif 'market_depth' in reason or '市场深度' in reason:
            return '🔍 市场深度'
        elif 'aggregated_alerts' in reason or '信号订阅' in reason:
            return '📢 AI全市场异动提醒 '
        else:
            # 如果无法识别，返回原始原因
            return f'📋 {reason}'
    
    def get_subscription_users_list(self, user_data: dict) -> dict:
        """获取订阅用户列表"""
        try:
            # 获取所有订阅用户
            subscription_users = []
            active_users = []
            inactive_users = []
            
            for user_id, user in user_data.items():
                user_id = int(user_id)  # 确保user_id是整数
                username = user.get('username', '未知用户')
                points = user.get('points', 0)
                subscription_active = user.get('subscription_active', False)
                subscription_types = user.get('subscription_types', [])
                register_time = user.get('register_time', '未知')
                last_active = user.get('last_active', '未知')
                
                # 获取订阅信息
                subscription_info = user.get('subscription', {})
                daily_cost = subscription_info.get('daily_cost', 50)  # 默认每日消费50积分
                last_charge_date = subscription_info.get('last_charge_date', None)
                
                # 格式化注册时间
                try:
                    if register_time != '未知':
                        register_dt = datetime.fromisoformat(register_time.replace('Z', '+00:00'))
                        register_formatted = register_dt.strftime('%Y-%m-%d %H:%M')
                    else:
                        register_formatted = '未知'
                except:
                    register_formatted = '未知'
                
                # 格式化最后活跃时间
                try:
                    if last_active != '未知':
                        last_active_dt = datetime.fromisoformat(last_active.replace('Z', '+00:00'))
                        last_active_formatted = last_active_dt.strftime('%Y-%m-%d %H:%M')
                    else:
                        last_active_formatted = '未知'
                except:
                    last_active_formatted = '未知'
                
                # 计算预计可用天数
                days_remaining = points // daily_cost if points >= daily_cost else 0
                
                # 计算到期日
                expiry_date = '未知'
                if days_remaining > 0:
                    try:
                        # 从今天开始计算到期日
                        today = datetime.now()
                        expiry_dt = today + timedelta(days=days_remaining)
                        expiry_date = expiry_dt.strftime('%Y-%m-%d')
                    except:
                        expiry_date = '计算失败'
                
                # 计算预计可用时间描述
                if days_remaining <= 0:
                    time_description = '已用完'
                elif days_remaining == 1:
                    time_description = '1天'
                elif days_remaining < 30:
                    time_description = f'{days_remaining}天'
                elif days_remaining < 365:
                    months = days_remaining // 30
                    remaining_days = days_remaining % 30
                    if remaining_days == 0:
                        time_description = f'{months}个月'
                    else:
                        time_description = f'{months}个月{remaining_days}天'
                else:
                    years = days_remaining // 365
                    remaining_days = days_remaining % 365
                    months = remaining_days // 30
                    final_days = remaining_days % 30
                    if months == 0 and final_days == 0:
                        time_description = f'{years}年'
                    elif final_days == 0:
                        time_description = f'{years}年{months}个月'
                    else:
                        time_description = f'{years}年{months}个月{final_days}天'
                
                # 构建用户信息
                user_info = {
                    'user_id': user_id,
                    'username': username,
                    'points': points,
                    'subscription_active': subscription_active,
                    'subscription_types': subscription_types,
                    'register_time': register_formatted,
                    'last_active': last_active_formatted,
                    'days_remaining': days_remaining,
                    'daily_cost': daily_cost,
                    'expiry_date': expiry_date,
                    'time_description': time_description,
                    'last_charge_date': last_charge_date or '未知'
                }
                
                if subscription_active:
                    active_users.append(user_info)
                else:
                    inactive_users.append(user_info)
                
                subscription_users.append(user_info)
            
            # 按积分排序
            active_users.sort(key=lambda x: x['points'], reverse=True)
            inactive_users.sort(key=lambda x: x['points'], reverse=True)
            
            return {
                'all_users': subscription_users,
                'active_users': active_users,
                'inactive_users': inactive_users,
                'total_count': len(subscription_users),
                'active_count': len(active_users),
                'inactive_count': len(inactive_users)
            }
            
        except Exception as e:
            logger.error(f"获取订阅用户列表失败: {e}")
            return {
                'all_users': [],
                'active_users': [],
                'inactive_users': [],
                'total_count': 0,
                'active_count': 0,
                'inactive_count': 0
            }
    
    def generate_report(self) -> str:
        """生成完整报告"""
        user_data, points_history = self.load_data()
        
        if not user_data or not points_history:
            return "错误: 无法加载数据文件"
        
        # 过滤管理员数据
        filtered_users, filtered_points = self.filter_admin_data(user_data, points_history)
        
        # 各项分析
        core_metrics = self.analyze_core_metrics(filtered_users, filtered_points)
        activity = self.analyze_activity(filtered_users)
        points_economy = self.analyze_points_economy(filtered_points)
        rankings = self.get_user_rankings(filtered_users)
        daily_activity = self.analyze_daily_activity(filtered_points)
        function_usage = self.analyze_function_usage(filtered_points)
        subscription_users = self.get_subscription_users_list(filtered_users)
        
        # 生成报告内容
        report = []
        
        # 标题头部 - 美化设计
        report.append("┌" + "─" * 58 + "┐")
        report.append("│" + " " * 10 + "🤖 加密货币交易机器人数据分析报告 📊" + " " * 10 + "│")
        report.append("├" + "─" * 58 + "┤")
        report.append(f"│  📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}" + " " * 16 + "│")
        report.append(f"│  📋 数据范围: 真实用户数据（已排除管理员测试账号）" + " " * 4 + "│")
        report.append(f"│  🎯 分析对象: {core_metrics['total_users']} 个真实用户, {core_metrics['points_records_count']} 条积分记录" + " " * (22 - len(str(core_metrics['total_users'])) - len(str(core_metrics['points_records_count']))) + "│")
        report.append("└" + "─" * 58 + "┘")
        
        # 核心指标 - 表格化展示
        report.append("")
        report.append("📈 核心业务指标")
        report.append("─" * 30)
        report.append("┌─────────────────────┬─────────────────────┐")
        report.append("│        指标名称      │        数值         │")
        report.append("├─────────────────────┼─────────────────────┤")
        report.append(f"│ 👥 真实用户总数      │ {core_metrics['total_users']:>10,} 人         │")
        report.append(f"│ 🔔 激活订阅用户      │ {core_metrics['active_subscribers']:>10,} 人         │")
        report.append(f"│ 📊 订阅激活率        │ {core_metrics['subscription_rate']:>13.1f}%        │")
        report.append(f"│ 💰 总积分池          │ {core_metrics['total_points_pool']:>10,} 积分       │")
        report.append(f"│ 💵 总充值金额        │ {core_metrics['total_recharge']:>10,} USDT      │")
        report.append(f"│ 📊 平均用户积分      │ {core_metrics['avg_points']:>13.2f} 积分       │")
        report.append(f"│ 💳 平均充值金额      │ {core_metrics['avg_recharge']:>13.2f} USDT      │")
        report.append(f"│ 📝 积分交易记录      │ {core_metrics['points_records_count']:>10,} 条         │")
        report.append("└─────────────────────┴─────────────────────┘")
        
        # 用户活跃度 - 进度条展示
        report.append("")
        report.append("🔥 用户活跃度分析")
        report.append("─" * 30)
        
        def create_progress_bar(percentage, width=20):
            filled = int(percentage / 100 * width)
            bar = "█" * filled + "░" * (width - filled)
            return f"[{bar}] {percentage:5.1f}%"
        
        report.append(f"📊 7天内活跃用户  : {activity['active_7d']:>4} 人")
        report.append(f"    活跃率        : {create_progress_bar(activity['active_7d_rate'])}")
        report.append("")
        report.append(f"📊 30天内活跃用户 : {activity['active_30d']:>4} 人")
        report.append(f"    活跃率        : {create_progress_bar(activity['active_30d_rate'])}")
        
        # 积分经济 - 卡片式展示
        report.append("")
        report.append("💰 积分使用情况")
        report.append("─" * 30)
        report.append("┌─────────────────────────────────────────────┐")
        report.append("│                积分流水概览                 │")
        report.append("├─────────────────────────────────────────────┤")
        report.append(f"│ 📈 总获得积分: {points_economy['total_income']:>15,} 积分        │")
        report.append(f"│ 📉 总消费积分: {points_economy['total_expense']:>15,} 积分        │")
        report.append(f"│ 💹 净积分流入: {points_economy['net_flow']:>15,} 积分        │")
        report.append("└─────────────────────────────────────────────┘")
        
        # 消费分类 - 排行榜式展示
        if points_economy['expense_categories']:
            report.append("")
            report.append("🛒 消费分类统计 (TOP 5)")
            report.append("─" * 30)
            total_expense = points_economy['total_expense']
            expense_items = sorted(points_economy['expense_categories'].items(), 
                                 key=lambda x: x[1], reverse=True)[:5]
            
            for i, (reason, amount) in enumerate(expense_items, 1):
                percentage = (amount / total_expense * 100) if total_expense > 0 else 0
                medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}️⃣"
                progress = create_progress_bar(percentage, 15)
                report.append(f"{medal} {reason}")
                report.append(f"    💸 {amount:>8,} 积分 {progress}")
                if i < len(expense_items):
                    report.append("")
        
        # 收入分类
        if points_economy['income_categories']:
            report.append("")
            report.append("💵 收入分类统计 (TOP 5)")
            report.append("─" * 30)
            total_income = points_economy['total_income']
            income_items = sorted(points_economy['income_categories'].items(), 
                                key=lambda x: x[1], reverse=True)[:5]
            
            for i, (reason, amount) in enumerate(income_items, 1):
                percentage = (amount / total_income * 100) if total_income > 0 else 0
                medal = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i}️⃣"
                progress = create_progress_bar(percentage, 15)
                report.append(f"{medal} {reason}")
                report.append(f"    💰 {amount:>8,} 积分 {progress}")
                if i < len(income_items):
                    report.append("")
        
        # 用户排行榜 - 表格化
        report.append("")
        report.append("🏆 用户排行榜")
        report.append("─" * 30)
        
        report.append("💰 积分排行榜 (TOP 10)")
        report.append("┌─────┬─────────────────┬──────────────┐")
        report.append("│ 排名│     用户名      │    积分      │")
        report.append("├─────┼─────────────────┼──────────────┤")
        for i, (uid, points, username) in enumerate(rankings['points_ranking'][:10], 1):
            rank_icon = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i:>2}"
            username_display = (username[:12] + "...") if len(username) > 15 else username
            report.append(f"│ {rank_icon} │ {username_display:<15} │ {points:>8,} 积分  │")
        report.append("└─────┴─────────────────┴──────────────┘")
        
        report.append("")
        report.append("💳 充值排行榜 (TOP 10)")
        recharge_users = [(uid, recharge, username) for uid, recharge, username in rankings['recharge_ranking'] if recharge > 0]
        if recharge_users:
            report.append("┌─────┬─────────────────┬──────────────┐")
            report.append("│ 排名│     用户名      │   充值金额   │")
            report.append("├─────┼─────────────────┼──────────────┤")
            for i, (uid, recharge, username) in enumerate(recharge_users[:10], 1):
                rank_icon = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else f"{i:>2}"
                username_display = (username[:12] + "...") if len(username) > 15 else username
                report.append(f"│ {rank_icon} │ {username_display:<15} │ {recharge:>8,} USDT │")
            report.append("└─────┴─────────────────┴──────────────┘")
        else:
            report.append("📊 暂无充值记录")
        
        # 功能消耗统计 - 饼图式展示
        report.append("")
        report.append("⚙️ 功能使用统计")
        report.append("─" * 30)
        
        # 累计功能消耗
        total_consumption = function_usage['total_consumption']
        if total_consumption:
            report.append("📊 累计功能消耗积分:")
            report.append("")
            total_func_consumption = sum(total_consumption.values())
            consumption_items = sorted(total_consumption.items(), key=lambda x: x[1], reverse=True)
            
            for i, (func_name, consumption) in enumerate(consumption_items[:8], 1):
                percentage = (consumption / total_func_consumption * 100) if total_func_consumption > 0 else 0
                progress = create_progress_bar(percentage, 12)
                icon = "⭐" if i <= 3 else "🔸"
                report.append(f"{icon} {func_name}")
                report.append(f"   💸 {consumption:>6,} 积分 {progress}")
                report.append("")
        
        # 近7天每日功能消耗
        daily_consumption = function_usage['daily_consumption']
        if daily_consumption:
            report.append("")
            report.append("📅 近7天每日功能消耗:")
            report.append("─" * 25)
            recent_dates = sorted(daily_consumption.keys(), reverse=True)[:7]
            for date in recent_dates:
                date_consumption = daily_consumption[date]
                if date_consumption:
                    total_day_consumption = sum(date_consumption.values())
                    report.append(f"📊 {date} (总计 {total_day_consumption:,} 积分)")
                    for func_name, consumption in sorted(date_consumption.items(), 
                                                       key=lambda x: x[1], reverse=True)[:5]:
                        report.append(f"    🔸 {func_name}: {consumption:,} 积分")
                else:
                    report.append(f"📊 {date}: 📴 无功能消耗")
                report.append("")
        
        # 近7天活动统计 - 时间线式展示
        report.append("")
        report.append("📊 近7天每日活动统计")
        report.append("─" * 30)
        report.append("┌─────────────┬─────┬─────┬─────┬─────┐")
        report.append("│    日期     │交易 │用户 │获得 │消费 │")
        report.append("├─────────────┼─────┼─────┼─────┼─────┤")
        sorted_dates = sorted(daily_activity.keys(), reverse=True)[:7]
        for date in sorted_dates:
            stats = daily_activity[date]
            report.append(f"│ {date} │{stats['transactions']:>4} │{stats['active_users']:>4} │{stats['points_earned']:>4} │{stats['points_spent']:>4} │")
        report.append("└─────────────┴─────┴─────┴─────┴─────┘")
        
        # 订阅用户列表
        report.append("")
        report.append("👥 当前订阅用户详细列表")
        report.append("─" * 50)
        
        # 活跃订阅用户
        if subscription_users['active_count'] > 0:
            report.append("✅ 活跃订阅用户")
            report.append("┌─────────────┬─────────────┬─────────┬─────────────┬─────────────┐")
            report.append("│   用户ID    │   用户名    │ 积分余额 │ 预计可用时间 │   到期日    │")
            report.append("├─────────────┼─────────────┼─────────┼─────────────┼─────────────┤")
            
            for user in subscription_users['active_users'][:20]:  # 显示前20个活跃用户
                user_id_str = str(user['user_id'])[:11]
                username_str = user['username'][:11]
                points = user['points']
                time_desc = user['time_description'][:11]
                expiry_date = user['expiry_date'][:11]
                
                report.append(f"│ {user_id_str:<11} │ {username_str:<11} │{points:>8,} │ {time_desc:<11} │ {expiry_date:<11} │")
            
            report.append("└─────────────┴─────────────┴─────────┴─────────────┴─────────────┘")
            
            if subscription_users['active_count'] > 20:
                report.append(f"... 还有 {subscription_users['active_count'] - 20} 个活跃用户未显示")
        else:
            report.append("❌ 暂无活跃订阅用户")
        
        # 非活跃订阅用户（只显示前10个）
        if subscription_users['inactive_count'] > 0:
            report.append("")
            report.append("⚠️ 非活跃订阅用户（前10名）")
            report.append("┌─────────────┬─────────────┬─────────┬─────────────┬─────────────┐")
            report.append("│   用户ID    │   用户名    │ 积分余额 │ 预计可用时间 │   到期日    │")
            report.append("├─────────────┼─────────────┼─────────┼─────────────┼─────────────┤")
            
            for user in subscription_users['inactive_users'][:10]:  # 显示前10个非活跃用户
                user_id_str = str(user['user_id'])[:11]
                username_str = user['username'][:11]
                points = user['points']
                time_desc = user['time_description'][:11]
                expiry_date = user['expiry_date'][:11]
                
                report.append(f"│ {user_id_str:<11} │ {username_str:<11} │{points:>8,} │ {time_desc:<11} │ {expiry_date:<11} │")
            
            report.append("└─────────────┴─────────────┴─────────┴─────────────┴─────────────┘")
            
            if subscription_users['inactive_count'] > 10:
                report.append(f"... 还有 {subscription_users['inactive_count'] - 10} 个非活跃用户未显示")
        
        # 订阅用户统计汇总
        report.append("")
        report.append("📊 订阅用户统计汇总")
        report.append("─" * 30)
        report.append("┌─────────────────────┬─────────────────────┐")
        report.append("│        状态         │        数量         │")
        report.append("├─────────────────────┼─────────────────────┤")
        report.append(f"│ ✅ 活跃订阅用户      │ {subscription_users['active_count']:>10,} 人         │")
        report.append(f"│ ⚠️ 非活跃订阅用户    │ {subscription_users['inactive_count']:>10,} 人         │")
        report.append(f"│ 📊 总订阅用户        │ {subscription_users['total_count']:>10,} 人         │")
        
        # 计算活跃率
        if subscription_users['total_count'] > 0:
            active_rate = (subscription_users['active_count'] / subscription_users['total_count']) * 100
        else:
            active_rate = 0
        
        report.append(f"│ 📈 订阅活跃率        │ {active_rate:>13.1f}%        │")
        report.append("└─────────────────────┴─────────────────────┘")
        
        # 添加积分余额分析
        report.append("")
        report.append("💰 积分余额分析")
        report.append("─" * 30)
        
        # 计算积分余额统计
        all_users_points = [user['points'] for user in subscription_users['all_users']]
        if all_users_points:
            total_points = sum(all_users_points)
            avg_points = total_points / len(all_users_points)
            max_points = max(all_users_points)
            min_points = min(all_users_points)
            
            # 按积分余额分组
            rich_users = len([p for p in all_users_points if p >= 10000])  # 富用户：>=10000积分
            medium_users = len([p for p in all_users_points if 1000 <= p < 10000])  # 中等用户：1000-9999积分
            poor_users = len([p for p in all_users_points if 100 <= p < 1000])  # 普通用户：100-999积分
            broke_users = len([p for p in all_users_points if p < 100])  # 贫困用户：<100积分
            
            report.append("┌─────────────────────┬─────────────────────┐")
            report.append("│        指标         │        数值         │")
            report.append("├─────────────────────┼─────────────────────┤")
            report.append(f"│ 💎 总积分池          │ {total_points:>10,} 积分       │")
            report.append(f"│ 📊 平均积分余额      │ {avg_points:>13.2f} 积分       │")
            report.append(f"│ 🔝 最高积分余额      │ {max_points:>10,} 积分       │")
            report.append(f"│ 🔻 最低积分余额      │ {min_points:>10,} 积分       │")
            report.append("├─────────────────────┼─────────────────────┤")
            report.append(f"│ 💰 富用户(≥10K积分)  │ {rich_users:>10,} 人         │")
            report.append(f"│ 💵 中等用户(1K-10K)  │ {medium_users:>10,} 人         │")
            report.append(f"│ 💳 普通用户(100-1K)  │ {poor_users:>10,} 人         │")
            report.append(f"│ 💸 贫困用户(<100)    │ {broke_users:>10,} 人         │")
            report.append("└─────────────────────┴─────────────────────┘")
        
        # 添加到期时间分析
        report.append("")
        report.append("⏰ 到期时间分析")
        report.append("─" * 30)
        
        # 按到期时间分组
        expiring_soon = len([user for user in subscription_users['all_users'] if 0 < user['days_remaining'] <= 7])  # 7天内到期
        expiring_month = len([user for user in subscription_users['all_users'] if 7 < user['days_remaining'] <= 30])  # 30天内到期
        expiring_quarter = len([user for user in subscription_users['all_users'] if 30 < user['days_remaining'] <= 90])  # 90天内到期
        long_term = len([user for user in subscription_users['all_users'] if user['days_remaining'] > 90])  # 长期用户
        expired = len([user for user in subscription_users['all_users'] if user['days_remaining'] <= 0])  # 已过期
        
        report.append("┌─────────────────────┬─────────────────────┐")
        report.append("│      到期时间       │        用户数       │")
        report.append("├─────────────────────┼─────────────────────┤")
        report.append(f"│ 🚨 7天内到期         │ {expiring_soon:>10,} 人         │")
        report.append(f"│ ⚠️ 30天内到期        │ {expiring_month:>10,} 人         │")
        report.append(f"│ 📅 90天内到期        │ {expiring_quarter:>10,} 人         │")
        report.append(f"│ 🔋 长期用户(>90天)   │ {long_term:>10,} 人         │")
        report.append(f"│ ❌ 已过期/无积分     │ {expired:>10,} 人         │")
        report.append("└─────────────────────┴─────────────────────┘")
        
        # 报告尾部
        report.append("")
        report.append("┌" + "─" * 58 + "┐")
        report.append("│" + " " * 16 + "📋 报告生成完毕 ✅" + " " * 18 + "│")
        report.append("│" + " " * 8 + "感谢使用加密货币交易机器人管理系统 🚀" + " " * 8 + "│")
        report.append("└" + "─" * 58 + "┘")
        
        return "\n".join(report)
    
    def save_report(self) -> Optional[str]:
        """保存报告到文件"""
        try:
            report_content = self.generate_report()
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"admin_data_report_{timestamp}.txt"
            
            # 写入文件
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(report_content)
            
            print(f"报告已生成: {filename}")
            print(f"文件大小: {len(report_content)} 字符")
            
            return filename
            
        except Exception as e:
            print(f"保存报告失败: {e}")
            return None

def main():
    """主函数"""
    try:
        print("=" * 80)
        print("管理员数据分析报告生成器")
        print("=" * 80)
        
        # 检查数据目录
        if not os.path.exists("data"):
            print("错误: 未找到 data 目录，请确保在正确的项目目录中运行此脚本")
            return False
        
        # 生成报告
        generator = AdminReportGenerator()
        filename = generator.save_report()
        
        if filename:
            print("任务完成! 报告已生成。")
            print(f"文件: {filename}")
            return True
        else:
            print("报告生成失败")
            return False
        
    except Exception as e:
        print(f"程序运行错误: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1) 