#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户数据字段一致性修复工具
直接修复 user_data.json 文件中的字段不一致问题
"""

import json
import os
from datetime import datetime
from typing import Dict, Any

def get_standard_template() -> Dict[str, Any]:
    """获取标准用户数据模板"""
    return {
        "user_id": 0,
        "username": "",
        "points": 20,
        "total_recharged": 0,
        "register_time": "",
        "last_active": "",
        "subscription_types": [],
        "subscription": {
            "is_active": False,
            "last_charge_date": None,
            "daily_cost": 50,
            "auto_renew": True
        },
        "subscription_active": False,
        "invited_bonus_given": False,
        "invited_by": None,
        "alerts_received": {
            "ai": 0,
            "transfer": 0,
            "total": 0,
            "open_interest": 0,
            "rsi": 0,
            "funding_rate": 0
        },
        "last_alert_time": None,
        "invitations": {}
    }

def fix_user_data():
    """修复用户数据"""
    user_data_file = "data/user_data.json"
    
    # 读取数据
    with open(user_data_file, 'r', encoding='utf-8') as f:
        users = json.load(f)
    
    # 创建备份
    backup_dir = "data/backups"
    os.makedirs(backup_dir, exist_ok=True)
    backup_file = f"{backup_dir}/user_data_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(backup_file, 'w', encoding='utf-8') as f:
        json.dump(users, f, ensure_ascii=False, indent=2)
    
    template = get_standard_template()
    fixed_count = 0
    
    for user_id, user_data in users.items():
        original_keys = set(user_data.keys())
        template_keys = set(template.keys())
        
        # 检查是否需要修复
        needs_fix = False
        
        # 添加缺失字段
        for key, default_value in template.items():
            if key not in user_data:
                if key == "alerts_received":
                    user_data[key] = {
                        "ai": 0,
                        "transfer": 0,
                        "total": 0,
                        "open_interest": 0,
                        "rsi": 0,
                        "funding_rate": 0
                    }
                elif key == "subscription":
                    user_data[key] = {
                        "is_active": False,
                        "last_charge_date": None,
                        "daily_cost": 50,
                        "auto_renew": True
                    }
                elif key == "invitations":
                    user_data[key] = {}
                else:
                    user_data[key] = default_value
                needs_fix = True
        
        # 清理 alerts_received 中的非标准字段
        if "alerts_received" in user_data:
            alerts = user_data["alerts_received"]
            standard_alerts = {
                "ai": alerts.get("ai", 0),
                "transfer": alerts.get("transfer", 0),
                "total": alerts.get("total", 0),
                "open_interest": alerts.get("open_interest", 0),
                "rsi": alerts.get("rsi", 0),
                "funding_rate": alerts.get("funding_rate", 0)
            }
            if alerts != standard_alerts:
                user_data["alerts_received"] = standard_alerts
                needs_fix = True
        
        # 移除多余的顶层字段
        extra_fields = ["last_charge_date", "subscription_cancelled_by_user", "subscription_cancelled_at"]
        for field in extra_fields:
            if field in user_data:
                del user_data[field]
                needs_fix = True
        
        # 确保用户ID正确
        if user_data.get("user_id") != int(user_id):
            user_data["user_id"] = int(user_id)
            needs_fix = True
        
        if needs_fix:
            fixed_count += 1
    
    # 保存修复后的数据
    with open(user_data_file, 'w', encoding='utf-8') as f:
        json.dump(users, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 修复完成！")
    print(f"📊 总用户数: {len(users)}")
    print(f"🔧 修复用户数: {fixed_count}")
    print(f"💾 备份文件: {backup_file}")

if __name__ == "__main__":
    fix_user_data()