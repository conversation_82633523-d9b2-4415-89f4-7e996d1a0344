#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
加密货币行情网站后端API
基于FastAPI，提供实时行情数据和用户数据接口
"""

import os
import json
import asyncio
import logging
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
import uvicorn

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 创建FastAPI应用
app = FastAPI(
    title="加密货币行情API",
    description="提供实时加密货币行情数据和用户数据",
    version="1.0.0"
)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 数据文件路径
DATA_DIR = Path("../../data")
CACHE_DIR = DATA_DIR / "cache"

# WebSocket连接管理器
class ConnectionManager:
    def __init__(self):
        self.active_connections: List[WebSocket] = []

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.append(websocket)
        logger.info(f"WebSocket连接建立，当前连接数: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        logger.info(f"WebSocket连接断开，当前连接数: {len(self.active_connections)}")

    async def send_personal_message(self, message: str, websocket: WebSocket):
        try:
            await websocket.send_text(message)
        except Exception as e:
            logger.error(f"发送个人消息失败: {e}")

    async def broadcast(self, message: str):
        disconnected = []
        for connection in self.active_connections:
            try:
                await connection.send_text(message)
            except Exception as e:
                logger.error(f"广播消息失败: {e}")
                disconnected.append(connection)

        # 清理断开的连接
        for connection in disconnected:
            self.disconnect(connection)

manager = ConnectionManager()

# 数据加载函数
def load_json_file(file_path: Path) -> Dict[str, Any]:
    """加载JSON文件"""
    try:
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        return {}
    except Exception as e:
        logger.error(f"加载文件失败 {file_path}: {e}")
        return {}

def get_market_data() -> Dict[str, Any]:
    """获取市场数据"""
    primary_cache = load_json_file(CACHE_DIR / "cache_data_primary.json")
    secondary_cache = load_json_file(CACHE_DIR / "cache_data_secondary.json")

    # 合并数据
    market_data = {
        "ticker_24hr": primary_cache.get("ticker_24hr_data", {}).get("data", []),
        "gainers": secondary_cache.get("gainers_data", {}).get("data", []),
        "losers": secondary_cache.get("losers_data", {}).get("data", []),
        "timestamp": datetime.now().isoformat()
    }

    return market_data

def format_crypto_data(crypto_list: List[Dict]) -> List[Dict]:
    """格式化加密货币数据"""
    formatted_data = []

    for crypto in crypto_list:
        try:
            # 提取币种名称（去掉USDT后缀）
            symbol = crypto.get("symbol", "")
            base_symbol = symbol.replace("USDT", "").replace("USDC", "")

            formatted_crypto = {
                "id": len(formatted_data) + 1,
                "symbol": base_symbol,
                "name": base_symbol,  # 实际应用中可以维护一个币种名称映射
                "price": float(crypto.get("lastPrice", 0)),
                "change_24h": float(crypto.get("priceChangePercent", 0)),
                "volume_24h": float(crypto.get("volume", 0)),
                "market_cap": float(crypto.get("quoteVolume", 0)),  # 使用交易额作为市值的近似
                "high_24h": float(crypto.get("highPrice", 0)),
                "low_24h": float(crypto.get("lowPrice", 0)),
                "original_symbol": symbol
            }
            formatted_data.append(formatted_crypto)
        except (ValueError, TypeError) as e:
            logger.warning(f"格式化数据失败 {crypto}: {e}")
            continue

    return formatted_data

# API路由
@app.get("/")
async def root():
    """根路径"""
    return {"message": "加密货币行情API", "version": "1.0.0"}

@app.get("/api/market/overview")
async def get_market_overview():
    """获取市场概览"""
    try:
        market_data = get_market_data()

        # 计算市场统计
        all_cryptos = market_data["ticker_24hr"]
        total_market_cap = sum(float(crypto.get("quoteVolume", 0)) for crypto in all_cryptos)
        total_volume = sum(float(crypto.get("volume", 0)) for crypto in all_cryptos)

        # 计算涨跌数量
        gainers_count = len([c for c in all_cryptos if float(c.get("priceChangePercent", 0)) > 0])
        losers_count = len([c for c in all_cryptos if float(c.get("priceChangePercent", 0)) < 0])

        overview = {
            "total_market_cap": total_market_cap,
            "total_volume_24h": total_volume,
            "total_cryptocurrencies": len(all_cryptos),
            "gainers_count": gainers_count,
            "losers_count": losers_count,
            "timestamp": market_data["timestamp"]
        }

        return overview
    except Exception as e:
        logger.error(f"获取市场概览失败: {e}")
        raise HTTPException(status_code=500, detail="获取市场概览失败")

@app.get("/api/cryptocurrencies")
async def get_cryptocurrencies(
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(100, ge=1, le=500, description="每页数量"),
    sort_by: str = Query("market_cap", description="排序字段"),
    order: str = Query("desc", description="排序方向"),
    search: Optional[str] = Query(None, description="搜索关键词")
):
    """获取加密货币列表"""
    try:
        market_data = get_market_data()
        all_cryptos = format_crypto_data(market_data["ticker_24hr"])

        # 搜索过滤
        if search:
            search_lower = search.lower()
            all_cryptos = [
                crypto for crypto in all_cryptos
                if search_lower in crypto["symbol"].lower() or search_lower in crypto["name"].lower()
            ]

        # 排序
        reverse = order.lower() == "desc"
        if sort_by in ["price", "change_24h", "volume_24h", "market_cap"]:
            all_cryptos.sort(key=lambda x: x.get(sort_by, 0), reverse=reverse)
        elif sort_by == "name":
            all_cryptos.sort(key=lambda x: x.get("symbol", ""), reverse=reverse)

        # 分页
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_cryptos = all_cryptos[start_idx:end_idx]

        return {
            "data": paginated_cryptos,
            "pagination": {
                "current_page": page,
                "per_page": limit,
                "total": len(all_cryptos),
                "total_pages": (len(all_cryptos) + limit - 1) // limit
            },
            "timestamp": market_data["timestamp"]
        }
    except Exception as e:
        logger.error(f"获取加密货币列表失败: {e}")
        raise HTTPException(status_code=500, detail="获取加密货币列表失败")

@app.get("/api/cryptocurrencies/{symbol}")
async def get_cryptocurrency_detail(symbol: str):
    """获取特定加密货币详情"""
    try:
        market_data = get_market_data()
        all_cryptos = market_data["ticker_24hr"]

        # 查找匹配的加密货币
        crypto_data = None
        for crypto in all_cryptos:
            if crypto.get("symbol", "").upper() == f"{symbol.upper()}USDT" or \
               crypto.get("symbol", "").upper() == f"{symbol.upper()}USDC":
                crypto_data = crypto
                break

        if not crypto_data:
            raise HTTPException(status_code=404, detail="加密货币未找到")

        # 格式化详细数据
        formatted_data = format_crypto_data([crypto_data])[0]

        # 添加额外的详细信息
        formatted_data.update({
            "open_price": float(crypto_data.get("openPrice", 0)),
            "close_time": crypto_data.get("closeTime"),
            "open_time": crypto_data.get("openTime"),
            "count": crypto_data.get("count", 0),
            "quote_volume": float(crypto_data.get("quoteVolume", 0))
        })

        return formatted_data
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取加密货币详情失败: {e}")
        raise HTTPException(status_code=500, detail="获取加密货币详情失败")

@app.get("/api/market/gainers")
async def get_top_gainers(limit: int = Query(10, ge=1, le=50)):
    """获取涨幅榜"""
    try:
        market_data = get_market_data()
        all_cryptos = format_crypto_data(market_data["ticker_24hr"])

        # 按涨幅排序
        gainers = sorted(all_cryptos, key=lambda x: x["change_24h"], reverse=True)[:limit]

        return {
            "data": gainers,
            "timestamp": market_data["timestamp"]
        }
    except Exception as e:
        logger.error(f"获取涨幅榜失败: {e}")
        raise HTTPException(status_code=500, detail="获取涨幅榜失败")

@app.get("/api/market/losers")
async def get_top_losers(limit: int = Query(10, ge=1, le=50)):
    """获取跌幅榜"""
    try:
        market_data = get_market_data()
        all_cryptos = format_crypto_data(market_data["ticker_24hr"])

        # 按跌幅排序
        losers = sorted(all_cryptos, key=lambda x: x["change_24h"])[:limit]

        return {
            "data": losers,
            "timestamp": market_data["timestamp"]
        }
    except Exception as e:
        logger.error(f"获取跌幅榜失败: {e}")
        raise HTTPException(status_code=500, detail="获取跌幅榜失败")

# WebSocket端点
@app.websocket("/ws/market")
async def websocket_market_data(websocket: WebSocket):
    """WebSocket市场数据推送"""
    await manager.connect(websocket)
    try:
        while True:
            # 每5秒推送一次市场数据
            market_data = get_market_data()
            await manager.send_personal_message(json.dumps(market_data), websocket)
            await asyncio.sleep(5)
    except WebSocketDisconnect:
        manager.disconnect(websocket)
    except Exception as e:
        logger.error(f"WebSocket错误: {e}")
        manager.disconnect(websocket)

# 静态文件服务（用于前端）
if Path("../frontend/build").exists():
    app.mount("/static", StaticFiles(directory="../frontend/build/static"), name="static")

    @app.get("/{full_path:path}")
    async def serve_frontend(full_path: str):
        """服务前端应用"""
        if full_path.startswith("api/"):
            raise HTTPException(status_code=404, detail="API路径未找到")

        file_path = Path("../frontend/build") / full_path
        if file_path.exists() and file_path.is_file():
            return FileResponse(file_path)
        else:
            return FileResponse("../frontend/build/index.html")

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )