// 加密货币数据类型定义

export interface Cryptocurrency {
  id: number;
  symbol: string;
  name: string;
  price: number;
  change_24h: number;
  volume_24h: number;
  market_cap: number;
  high_24h: number;
  low_24h: number;
  original_symbol: string;
}

export interface CryptocurrencyDetail extends Cryptocurrency {
  open_price: number;
  close_time: number;
  open_time: number;
  count: number;
  quote_volume: number;
}

export interface MarketOverview {
  total_market_cap: number;
  total_volume_24h: number;
  total_cryptocurrencies: number;
  gainers_count: number;
  losers_count: number;
  timestamp: string;
}

export interface ApiResponse<T> {
  data: T;
  timestamp: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T> {
  pagination: {
    current_page: number;
    per_page: number;
    total: number;
    total_pages: number;
  };
}

export interface SortConfig {
  field: string;
  direction: 'asc' | 'desc';
}

export interface FilterConfig {
  search?: string;
  minPrice?: number;
  maxPrice?: number;
  minChange?: number;
  maxChange?: number;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: 'market_data' | 'error' | 'connection';
  data?: any;
  message?: string;
}

// 图表数据类型
export interface ChartDataPoint {
  timestamp: number;
  price: number;
  volume?: number;
}

export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    borderColor: string;
    backgroundColor: string;
    fill?: boolean;
  }[];
}

// 用户偏好设置
export interface UserPreferences {
  theme: 'light' | 'dark';
  currency: 'USD' | 'CNY' | 'EUR';
  refreshInterval: number;
  favoriteCoins: string[];
  tableColumns: string[];
}

// API错误类型
export interface ApiError {
  message: string;
  status: number;
  details?: any;
}