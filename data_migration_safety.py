#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全的用户数据迁移和修复工具
"""

import json
import os
from datetime import datetime
from typing import Dict, Any

def create_backup():
    """创建数据备份"""
    backup_dir = "data/backups"
    os.makedirs(backup_dir, exist_ok=True)
    
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_file = f"{backup_dir}/user_data_backup_{timestamp}.json"
    
    # 读取原始数据
    with open("data/user_data.json", "r", encoding="utf-8") as f:
        data = json.load(f)
    
    # 保存备份
    with open(backup_file, "w", encoding="utf-8") as f:
        json.dump(data, f, ensure_ascii=False, indent=2)
    
    print(f"✅ 备份已创建: {backup_file}")
    return backup_file, data

def get_standard_template():
    """获取标准用户数据模板"""
    return {
        "user_id": 0,
        "username": "",
        "points": 20,
        "total_recharged": 0,
        "register_time": "",
        "last_active": "",
        "subscription_types": [],
        "subscription": {
            "is_active": False,
            "last_charge_date": None,
            "daily_cost": 50,
            "auto_renew": True
        },
        "subscription_active": False,
        "invited_bonus_given": False,
        "invited_by": None,
        "alerts_received": {
            "ai": 0,
            "transfer": 0,
            "total": 0,
            "open_interest": 0,
            "rsi": 0,
            "funding_rate": 0
        },
        "last_alert_time": None,
        "invitations": {}
    }

def fix_user_data(users_data: Dict[str, Any]) -> int:
    """修复用户数据，返回修复的用户数量"""
    template = get_standard_template()
    fixed_count = 0
    
    for user_id, user_data in users_data.items():
        needs_fix = False
        
        # 1. 添加缺失的字段
        for field, default_value in template.items():
            if field not in user_data:
                if field == "invitations":
                    user_data[field] = {}
                elif field == "invited_bonus_given":
                    user_data[field] = False
                elif field == "invited_by":
                    user_data[field] = None
                elif field == "alerts_received":
                    user_data[field] = {
                        "ai": 0,
                        "transfer": 0,
                        "total": 0,
                        "open_interest": 0,
                        "rsi": 0,
                        "funding_rate": 0
                    }
                else:
                    user_data[field] = default_value
                needs_fix = True
        
        # 2. 修复 alerts_received 字段
        if "alerts_received" in user_data:
            alerts = user_data["alerts_received"]
            
            # 移除非标准字段
            non_standard_keys = [k for k in alerts.keys() if k not in ["ai", "transfer", "total", "open_interest", "rsi", "funding_rate"]]
            for key in non_standard_keys:
                del alerts[key]
                needs_fix = True
            
            # 确保所有标准字段存在
            for field in ["ai", "transfer", "total", "open_interest", "rsi", "funding_rate"]:
                if field not in alerts:
                    alerts[field] = 0
                    needs_fix = True
        
        # 3. 移除多余的顶层字段
        extra_fields = ["last_charge_date", "subscription_cancelled_by_user", "subscription_cancelled_at"]
        for field in extra_fields:
            if field in user_data:
                del user_data[field]
                needs_fix = True
        
        # 4. 确保用户ID正确
        if user_data.get("user_id") != int(user_id):
            user_data["user_id"] = int(user_id)
            needs_fix = True
        
        if needs_fix:
            fixed_count += 1
    
    return fixed_count

def main():
    """主函数"""
    print("🚀 开始用户数据一致性修复...")
    
    # 创建备份并读取数据
    backup_file, users_data = create_backup()
    print(f"📊 读取了 {len(users_data)} 个用户数据")
    
    # 修复数据
    print("🔧 开始修复数据...")
    fixed_count = fix_user_data(users_data)
    
    # 保存修复后的数据
    print("💾 保存修复后的数据...")
    with open("data/user_data.json", "w", encoding="utf-8") as f:
        json.dump(users_data, f, ensure_ascii=False, indent=2)
    
    # 输出结果
    print(f"✅ 修复完成！")
    print(f"📊 总用户数: {len(users_data)}")
    print(f"🔧 修复用户数: {fixed_count}")
    print(f"✨ 未修复用户数: {len(users_data) - fixed_count}")
    print(f"💾 备份文件: {backup_file}")

if __name__ == "__main__":
    main()