import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import MarketOverview from './components/MarketOverview';
import CryptocurrencyTable from './components/CryptocurrencyTable';
import TopMovers from './components/TopMovers';
import Footer from './components/Footer';
import './index.css';

function App() {
  return (
    <Router>
      <div className="min-h-screen bg-gray-50">
        <Header />

        <main className="container mx-auto px-4 py-8">
          <Routes>
            <Route path="/" element={
              <div className="space-y-8">
                {/* 市场概览 */}
                <MarketOverview />

                {/* 涨跌幅榜 */}
                <TopMovers />

                {/* 加密货币列表 */}
                <CryptocurrencyTable />
              </div>
            } />

            <Route path="/markets" element={
              <div className="space-y-8">
                <h1 className="text-3xl font-bold text-gray-900">市场行情</h1>
                <CryptocurrencyTable />
              </div>
            } />

            <Route path="/gainers" element={
              <div className="space-y-8">
                <h1 className="text-3xl font-bold text-gray-900">涨幅榜</h1>
                <TopMovers showOnlyGainers />
              </div>
            } />

            <Route path="/losers" element={
              <div className="space-y-8">
                <h1 className="text-3xl font-bold text-gray-900">跌幅榜</h1>
                <TopMovers showOnlyLosers />
              </div>
            } />
          </Routes>
        </main>

        <Footer />
      </div>
    </Router>
  );
}

export default App;